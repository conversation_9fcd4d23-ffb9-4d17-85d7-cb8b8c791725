import { Router } from "express";
import userController from "../../controller/user.controller";
import userIniviteController from "../../controller/userInvitation.controller";
import userValidator from "../../validators/user.validator";
const router: Router = Router();
import { multerS3 } from "../../helper/upload.service";
import { FILE_UPLOAD_CONSTANT } from "../../helper/constant";

// Initialize multerS3 uploader with proper error handling
const multerS3Upload = multerS3(
  process.env.NODE_ENV || "development",
  FILE_UPLOAD_CONSTANT.USER_PROFILE_API.folder,
);

router.post(
  "/create",
  multerS3Upload.upload("user_avatar"),
  userController.createUser,
);
router.put(
  "/update/:user_id",
  multerS3Upload.fields([
    { name: "user_avatar", maxCount: 1 },
    { name: "user_signature", maxCount: 1 },
  ]),
  userController.updateUser,
);
router.post(
  "/reset-password",
  userValidator.resetPassword(),
  userController.resetPassword,
);
router.post(
  "/set-login-pin",
  userValidator.setLoginPin(),
  userController.setLoginPin,
);
router.post(
  "/reset-pin",
  userValidator.resetLoginPin(),
  userController.resetLoginPin,
);
router.put(
  "/update-profile",
  multerS3Upload.upload("user_avatar"),
  userController.updateUserProfile,
);
router.get("/view-profile", userController.viewProfile);
router.delete("/delete/:user_id", userController.deleteUser);
router.get("/list", userController.getUserList);
router.get("/role-list", userController.getRoleList);
router.post(
  "/switch-role",
  userValidator.switchRole(),
  userController.switchUserRole,
);
router.get("/re-enter-pin", userController.reEnterPinAfterSometime);
router.post("/logout", userController.logOut);
router.get("/get-one-user/:user_id", userController.getUserById);
router.get("/get-activity-log", userController.getActivityLog);
router.get("/get-activity-by-user/:user_id", userController.getUserActivityLog);
router.get("/get-permission-list", userController.getPermissionList);
router.delete("/delete-account/:user_id", userController.deleteUserAccount);
router.post(
  "/login-with-pin",
  userValidator.loginWithPin(),
  userController.loginWithPin,
);
router.post("/update-notification-token", userController.updateUserToken);
router.post("/reset-user-password", userController.changeUserPassword);
router.post("/reset-user-profile", userController.resetUserProfile);
router.get(
  "/get-user-update-activity/:user_id",
  userController.getUserUpdateHistory,
);
router.post("/send-invitation", userIniviteController.sendInivitation);
router.get("/get-invite-user-list", userIniviteController.getUserInviteList);

router.get("/get-geo-list", userController.getGeoList);

router.put(
  "/update-work-schedule",
  userValidator.update_work_schedule(),
  userController.upateWorkSchedule,
);

router.post("/send-reinvitation", userIniviteController.sendReInvitation);
router.post("/assign-policy", userController.assignPolicy);

// Add routes for storage usage and file retrieval
router.get(
  "/storage-usage/:organization_id?",
  userController.calculateStorageUsage,
);
router.get("/get-file", userController.getFileFromS3);


router.get("/migrate-s3", userController.migrateLocaltoS3)

router.get("/get-user-fields", userController.getUserFields)

router.get("/export-users", userController.exportUsers)

router.post("/store-user-field-sequence", userController.storeUserFieldSequence)

router.get("/get-stored-user-field-order", userController.getStoredUserFieldOrder)

router.get("/migrate-permission", userController.migratePermission)

export default router;
