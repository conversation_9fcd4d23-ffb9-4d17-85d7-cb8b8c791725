import { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Resignation, resignation_status as resign_status } from "../models/Resignation";
import { Op, QueryTypes, Sequelize } from "sequelize";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { User } from "../models/User";
import { createNotification, getAdminStaffs, getOrganizationLogo, sendEmailNotification, permittedForAdminEnhanced, validateModulePermission } from "../helper/common";
import { Role, role_status } from "../models/Role";
import { sequelize } from "../models";
import { Branch, branch_status } from "../models/Branch";
import { Department, department_status } from "../models/Department";
import { CHECKLISTTYPE, EMAIL_ADDRESS, NOTIFICATION_TYPE, NOTIFICATIONCONSTANT, REDIRECTION_TYPE, ROLE_CONSTANT, ROLE_PERMISSIONS } from "../helper/constant";
import { CheckList, checklist_status, checklist_type } from "../models/CheckList";
import { ResignationRemark } from "../models/ResignationRemark";
import { UserLeavingCheckList, user_leaving_checklist_status } from "../models/UserLeavingCheckList";
import _ from "lodash";
import { UserBranch } from "../models/UserBranch";


/**
 * User Resignation
 * @param req
 * @param res
 * @returns
 */

const sendResignation = async (req: any, res: any) => {
    try {
        const { resignation_subject, resignation_reason } = req.body
        const getUserDetail: any = await User.findOne({ attributes: ['user_first_name', 'user_last_name', 'branch_id', 'id'], where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true })

        if (!getUserDetail) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("ERROR_USER_NOT_FOUND"),
            });
        }
        const checkRequestExist = await Resignation.findOne({
            attributes: ['id'],
            where: {
                user_id: getUserDetail.id,
                resignation_status: {
                    [Op.in]: [resign_status.ACCEPTED, resign_status.IN_DISCUSSION, resign_status.PENDING]
                }
            }, raw: true
        })

        if (checkRequestExist) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_RESIGNATION_REQUEST_EXIST"),
            });
        }

        const sendResignation = await Resignation.setHeaders(req).create({
            resignation_subject: resignation_subject,
            resignation_reason: resignation_reason,
            resignation_status: resign_status.PENDING,
            user_id: req.user.id,
            created_by: req.user.id
        } as any)

        if (sendResignation) {
            const templateData = {
                name: `${getUserDetail.user_first_name} ${getUserDetail.user_last_name}`
            };
            const getAdmins = await getAdminStaffs(getUserDetail.branch_id, req.user.id, null, req.user.organization_id);
            await createNotification(getAdmins, req, NOTIFICATION_TYPE.ADMIN, NOTIFICATIONCONSTANT.RESIGNATION_APPLY.content(templateData.name), NOTIFICATIONCONSTANT.RESIGNATION_APPLY.heading, REDIRECTION_TYPE.RESIGNATION, sendResignation.id, { resignation_id: sendResignation.id })

            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_RESIGNATION_SENDED"),
            });
        } else {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_RESIGNATION_NOT_SEND"),
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

/**
 * Get Resignation List
 * @param req
 * @param res
 * @returns
 */


const getResignationList = async (req: any, res: any) => {
    try {
        const {
            size,
            page,
            search,
            branch_id,
            department_id,
            status,
            role_id,
            applied_date,
            last_serving_date,
            updated_date
        } = req.query;

        const { limit, offset } = getPagination(Number(page), Number(size));

        const getUserDetail: any = await User.findOne({
            attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'organization_id', 'branch_id'],
            where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
        });

        const getUserCurrentRole: any = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: req.headers["platform-type"] == "web" ? getUserDetail.web_user_active_role_id : getUserDetail.user_active_role_id, role_status: role_status.ACTIVE }, raw: true })
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'resignation', // Resignation module slug
            ROLE_PERMISSIONS.VIEW,
            req?.headers?.["platform-type"]
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.DIRECTOR,
        //         ROLE_CONSTANT.AREA_MANAGER,
        //         ROLE_CONSTANT.HR,
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.ACCOUNTANT,
        //         ROLE_CONSTANT.HOTEL_MANAGER,
        //     ]
        // );

        // Also check old permission system for backward compatibility
        const oldPermissionCheck = getUserCurrentRole && [
            ROLE_CONSTANT.SUPER_ADMIN,
            ROLE_CONSTANT.ADMIN,
            ROLE_CONSTANT.DIRECTOR,
            ROLE_CONSTANT.AREA_MANAGER,
            ROLE_CONSTANT.HR,
            ROLE_CONSTANT.BRANCH_MANAGER,
            ROLE_CONSTANT.ACCOUNTANT,
            ROLE_CONSTANT.HOTEL_MANAGER,
        ].includes(getUserCurrentRole?.role_name);

        // User has permission if any check passes
        const hasPermission = checkModulePermission || /* checkAdminPermission || */ oldPermissionCheck;

        if (!hasPermission) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PERMISSION_DENIED"),
            });
        }

        const findUserRole: any = await Role.findOne({
            attributes: ['id', 'role_name'],
            where: { id: req.headers["platform-type"] == "web" ? getUserDetail.web_user_active_role_id : getUserDetail.user_active_role_id }, raw: true
        });

        const whereObj: any = {
            user_id: { [Op.not]: req.user.id }
        }

        if (status) {
            whereObj.resignation_status = status;
        }


        if (applied_date) {
            whereObj.createdAt = sequelize.where(sequelize.fn('DATE', sequelize.col('Resignation.createdAt')), {
                [Op.eq]: applied_date
            });
        }

        if (last_serving_date) {
            whereObj.last_serving_date = sequelize.where(sequelize.fn('DATE', sequelize.col('Resignation.last_serving_date')), {
                [Op.eq]: last_serving_date
            });
        }

        if (updated_date) {
            whereObj.updateAt = sequelize.where(sequelize.fn('DATE', sequelize.col('Resignation.updatedAt')), {
                [Op.eq]: updated_date
            });
        }


        // Search
        if (search) {
            whereObj[Op.or] = [
                Sequelize.where(
                    Sequelize.fn(
                        "concat",
                        Sequelize.col("user_first_name"),
                        " ",
                        Sequelize.col("user_last_name"),
                    ),
                    {
                        [Op.like]: `%${search}%`,
                    },
                ),
            ];
        }

        const getResignationQuery: any = {
            include: [
                {
                    model: User,
                    as: "resign_user",
                    attributes: [
                        "id",
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("resign_user.user_first_name"),
                                " ",
                                sequelize.col("resign_user.user_last_name"),
                            ),
                            "user_full_name"
                        ],
                        "user_email",
                        [
                            sequelize.literal(
                                `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = resign_user.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', resign_user.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = resign_user.user_avatar))
          END`
                            ),
                            "user_avatar_link",
                        ],
                        "user_avatar",
                        "employment_number"
                    ],
                    where: {
                        organization_id: getUserDetail.organization_id, // Ensure the user belongs to the same organization
                    },
                    include: [
                        {
                            model: Branch,
                            as: "branch",
                            attributes: ["id", "branch_name"],
                            where: branch_id
                                ? { id: branch_id, branch_status: branch_status.ACTIVE }
                                : { branch_status: branch_status.ACTIVE },
                            required: branch_id ? true : false,
                        },
                        {
                            model: Department,
                            as: "department",
                            attributes: ["department_name"],
                            where: department_id
                                ? {
                                    id: department_id,
                                    department_status: department_status.ACTIVE
                                }
                                : { department_status: department_status.ACTIVE },
                            required: department_id ? true : false,
                        },
                        {
                            model: Role,
                            as: "role",
                            attributes: ["role_name"],
                            where: role_id
                                ? { id: role_id, role_status: role_status.ACTIVE }
                                : { role_status: role_status.ACTIVE },
                            required: role_id ? true : false,
                        },
                    ],
                    required: true,
                },
            ],
            attributes: [
                "id",
                "resignation_subject",
                "resignation_reason",
                "user_id",
                "last_serving_date",
                "resignation_status",
                "createdAt",
                "updatedAt",
                [
                    sequelize.literal(
                        `(CASE
                            WHEN (resignation_status = 'accepted' AND (SELECT COUNT(*) FROM nv_checklist WHERE (type & ${CHECKLISTTYPE.ADMIN}) = ${CHECKLISTTYPE.ADMIN} AND checklist_status = '${checklist_status.ACTIVE}' AND checklist_type = '${checklist_type.LEAVING}') = (SELECT COUNT(*) FROM nv_user_leaving_checklist WHERE to_user_id = Resignation.user_id AND from_user_id IS NOT NULL AND status = '${user_leaving_checklist_status.COMPLETED}')) THEN 'completed'
                            WHEN (resignation_status != 'accepted' OR (SELECT COUNT(*) FROM nv_user_leaving_checklist WHERE to_user_id = Resignation.user_id AND from_user_id IS NOT NULL AND status = '${user_leaving_checklist_status.COMPLETED}') = 0) THEN 'pending'
                            ELSE 'ongoing'
                          END)`,
                    ),
                    "leaving_checklist_status",
                ],
            ],
            where: whereObj,
            order: [['createdAt', 'DESC']],
            nest: true,
            raw: true
        }

        if (page && size) {
            getResignationQuery.limit = Number(limit);
            getResignationQuery.offset = Number(offset);
        }

        if (
            (findUserRole &&
                (findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
                    findUserRole.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER ||
                    findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER ||
                    findUserRole.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER || findUserRole.role_name == ROLE_CONSTANT.AREA_MANAGER
                ))) {


            const findUserBranch = await UserBranch.findAll({
                where: {
                    user_id: req.user.id
                }, raw: true
            })
            if (findUserRole.role_name == ROLE_CONSTANT.AREA_MANAGER && findUserBranch.length > 0) {
                whereObj[Op.and] = [
                    Sequelize.where(Sequelize.col("resign_user.branch_id"), {
                        [Op.in]: findUserBranch.map((branch: any) => branch.branch_id),
                    }),
                ];
            } else {
                whereObj[Op.and] = [
                    Sequelize.where(Sequelize.col("resign_user.branch_id"), {
                        [Op.eq]: getUserDetail?.branch_id,
                    }),
                ];
            }
            const getChildRolesQuery = `
                WITH RECURSIVE ChildRoles AS (
                SELECT id, role_name, parent_role_id
                FROM nv_roles
                WHERE id = :activeRoleId
                UNION ALL
                SELECT r.id, r.role_name, r.parent_role_id
                FROM nv_roles r
                INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
                )
                SELECT id
                FROM ChildRoles
                WHERE id != :activeRoleId`;

            // Execute recursive query to find child roles
            const getChildRoles = await sequelize.query(getChildRolesQuery, {
                replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
                type: QueryTypes.SELECT,
            });

            // Build WHERE clause for user roles based on child roles
            let whereStr = '';
            getChildRoles.forEach((child_role: any, index: number) => {
                if (index > 0) {
                    whereStr += ' OR ';
                }
                whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = resign_user.id)) > 0`;
            });

            if (whereStr) {
                whereObj[Op.and].push(sequelize.literal(`(${whereStr})`));
            }
        }
        const { rows: resignationList, count } = await Resignation.findAndCountAll(getResignationQuery)
        const { total_pages } = getPaginatedItems(
            size,
            page,
            count || 0,
        );
        return res.status(StatusCodes.OK).json({
            status: true,
            resignationList: resignationList,
            message: res.__("SUCCESS_FETCHED"),
            count: count,
            page: parseInt(page),
            size: parseInt(size),
            total_pages
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

/**
 * Get Resignation By Id
 * @param req
 * @param res
 * @returns
 */
const getResignationById = async (req: any, res: Response) => {
    try {
        const { resignation_id } = req.query

        let active_resignation_id = resignation_id
        if (!resignation_id) {
            const checkRequestExist = await Resignation.findOne({
                where: {
                    user_id: req.user.id,
                    resignation_status: {
                        [Op.in]: [resign_status.ACCEPTED, resign_status.IN_DISCUSSION, resign_status.PENDING]
                    },
                }, raw: true
            })

            active_resignation_id = checkRequestExist ? checkRequestExist.id : null
        }

        let getResignationDetail: any = await Resignation.findOne({
            attributes: [
                "id",
                "resignation_subject",
                "resignation_reason",
                "user_id",
                "last_serving_date",
                "resignation_status",
                "createdAt",
                "updatedAt",
                [
                    sequelize.literal(
                        `(CASE
                            WHEN ((SELECT COUNT(*) FROM nv_checklist WHERE (type & ${CHECKLISTTYPE.ADMIN}) = ${CHECKLISTTYPE.ADMIN} AND checklist_status = '${checklist_status.ACTIVE}' AND checklist_type = '${checklist_type.LEAVING}') = (SELECT COUNT(*) FROM nv_user_leaving_checklist WHERE to_user_id = Resignation.user_id AND from_user_id IS NOT NULL AND status = '${user_leaving_checklist_status.COMPLETED}')) THEN 'completed'
                            WHEN ((SELECT COUNT(*) FROM nv_user_leaving_checklist WHERE to_user_id = Resignation.user_id AND from_user_id IS NOT NULL AND status = '${user_leaving_checklist_status.COMPLETED}') = 0) THEN 'pending'
                            ELSE 'ongoing'
                          END)`,
                    ),
                    "leaving_checklist_status",
                ],
            ],
            include: [{
                model: User,
                as: "resign_user",
                attributes: [
                    "id",
                    [
                        sequelize.fn(
                            "concat",
                            sequelize.col("resign_user.user_first_name"),
                            " ",
                            sequelize.col("resign_user.user_last_name"),
                        ),
                        "user_full_name",
                    ],
                    "employment_number"
                ],
                where: { organization_id: req.user.organization_id },
                include: [
                    {
                        model: Branch,
                        as: "branch",
                        attributes: ["id", "branch_name"],
                        where: { branch_status: branch_status.ACTIVE, organization_id: req.user.organization_id },
                        required: false
                    },
                    {
                        model: Department,
                        as: "department",
                        attributes: ["department_name"],
                        where: { department_status: department_status.ACTIVE, organization_id: req.user.organization_id },
                        required: false,
                    },
                    {
                        model: Role,
                        as: "role",
                        attributes: ["role_name"],
                        required: false,
                    },
                ],
                required: false,
            }, {
                model: User,
                as: "resignation_updated_user",
                attributes: [
                    "id",
                    [
                        sequelize.fn(
                            "concat",
                            sequelize.col("resignation_updated_user.user_first_name"),
                            " ",
                            sequelize.col("resignation_updated_user.user_last_name"),
                        ),
                        "user_full_name",
                    ],
                    "employment_number"
                ],
                where: { organization_id: req.user.organization_id },
                include: [
                    {
                        model: Branch,
                        as: "branch",
                        attributes: ["id", "branch_name"],
                        where: { branch_status: branch_status.ACTIVE, organization_id: req.user.organization_id },
                        required: false,
                    },
                    {
                        model: Department,
                        as: "department",
                        attributes: ["department_name"],
                        where: { department_status: department_status.ACTIVE, organization_id: req.user.organization_id },
                        required: false,
                    },
                    {
                        model: Role,
                        as: "role",
                        attributes: ["role_name"],
                        required: false,
                    },
                ],
                required: false,
            }],
            where: { id: active_resignation_id },
            raw: true,
            nest: true
        })

        if (getResignationDetail) {

            getResignationDetail = JSON.parse(JSON.stringify(getResignationDetail));
            const getResignationRemark = await ResignationRemark.findAll({
                attributes: ['remarks', 'createdAt'],
                include: [{
                    model: User,
                    as: "resign_remark_user",
                    attributes: [
                        "id",
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("resign_remark_user.user_first_name"),
                                " ",
                                sequelize.col("resign_remark_user.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: { organization_id: req.user.organization_id },
                    include: [
                        {
                            model: Branch,
                            as: "branch",
                            attributes: ["id", "branch_name"],
                            where: { branch_status: branch_status.ACTIVE, organization_id: req.user.organization_id },
                            required: false
                        },
                        {
                            model: Department,
                            as: "department",
                            attributes: ["department_name"],
                            where: { department_status: department_status.ACTIVE, organization_id: req.user.organization_id },
                            required: false
                        },
                        {
                            model: Role,
                            as: "role",
                            attributes: ["role_name"],
                            required: false
                        },
                    ],
                    required: false,
                }],
                where: { resignation_id: getResignationDetail.id },
                raw: true,
                nest: true
            })
            getResignationDetail.resignation_remarks = getResignationRemark
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: getResignationDetail ? getResignationDetail : {},
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

/**
 * Update Resignation request
 * @param req
 * @param res
 * @returns
 */
const updateResignationRequest = async (req: any, res: any) => {
    try {
        const { resignation_status, remarks, last_serving_date } = req.body
        const { resignation_id } = req.params;

        const checkResignationRequestExist = await Resignation.findOne({
            attributes: ['id', 'user_id', 'resignation_status'],
            where: {
                id: resignation_id
            }, raw: true
        })
        if (!checkResignationRequestExist) {
            return res
                .status(StatusCodes.BAD_REQUEST)
                .json({ status: false, message: res.__("FAIL_DATA_NOT_FOUND") });
        }

        const updateObj: any = {
            resignation_status: resignation_status,
        }

        if (resignation_status == resign_status.ACCEPTED && last_serving_date)
            updateObj.last_serving_date = last_serving_date
        else
            updateObj.last_serving_date = null

        if (checkResignationRequestExist.resignation_status != resignation_status) {
            updateObj.updated_by = req.user.id
        }

        const updateResignation = await Resignation.setHeaders(req).update(updateObj, {
            where: {
                id: resignation_id
            },
        },)

        if (updateResignation || remarks) {

            if (remarks && (checkResignationRequestExist.resignation_status != resignation_status || resignation_status == resign_status.IN_DISCUSSION)) {

                await ResignationRemark.setHeaders(req).create({
                    remarks: remarks,
                    resignation_id: resignation_id,
                    user_id: req.user.id,
                    created_by: req.user.id
                } as any)

                // email and notification 
                const getRequestUserDetail: any = await User.findOne({
                    attributes: ['id', 'user_first_name', 'user_last_name', 'user_email', 'organization_id', 'appToken', 'webAppToken'],
                    where: { id: checkResignationRequestExist.user_id, organization_id: req.user.organization_id }, raw: true
                });

                const templateData: any = {
                    name: `${getRequestUserDetail.user_first_name} ${getRequestUserDetail.user_last_name}`,
                    admin: req.user.user_first_name,
                    remark: remarks,
                    email: getRequestUserDetail.user_email,
                    mail_type: 'resignation_response',
                    ORGANIZATION_LOGO: await getOrganizationLogo(getRequestUserDetail.organization_id),
                    LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
                    ADDRESS: EMAIL_ADDRESS.ADDRESS,
                    PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                    EMAIL: EMAIL_ADDRESS.EMAIL,
                    ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                    smtpConfig: 'INFO',
                    action: resignation_status.charAt(0).toUpperCase() +
                        resignation_status.slice(1)
                };
                templateData.resign_status = resignation_status;

                if (resignation_status == resign_status.IN_DISCUSSION) {
                    templateData.resign_status = "discussed";
                }
                await sendEmailNotification(templateData)
                if (templateData.resign_status != 'pending') {
                    await createNotification([getRequestUserDetail], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.RESIGNATION_RESPONSE.content(templateData.admin, templateData.resign_status), NOTIFICATIONCONSTANT.RESIGNATION_RESPONSE.heading, REDIRECTION_TYPE.RESIGNATION, resignation_id, { resignation_id: resignation_id })
                }
            }
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_RESIGNATION_UPDATED"),
            });
        } else {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_RESIGNATION_NOT_SEND"),
            });
        }

    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

/**
 * Get Leaving checklist 
 * @param req
 * @param res
 * @returns
 */

const getLeavingChecklist = async (req: any, res: Response) => {
    try {
        const { resignation_id } = req.params;

        const checkRequestExist = await Resignation.findOne({
            attributes: ['id', 'user_id'],
            where: {
                id: resignation_id,
                resignation_status: {
                    [Op.in]: [resign_status.ACCEPTED]
                }
            }, raw: true
        })

        if (!checkRequestExist) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_DATA_NOT_FOUND"),
            });
        }

        const getUserDetail: any = await User.findOne({ attributes: ['web_user_active_role_id', 'user_active_role_id'], where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true })

        if (!getUserDetail) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
        }
        const getUserCurrentRole: any = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: req.headers["platform-type"] == "web" ? getUserDetail?.web_user_active_role_id : getUserDetail?.user_active_role_id, role_status: role_status.ACTIVE } })
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'resignation', // Resignation module slug
            ROLE_PERMISSIONS.VIEW,
            req?.headers?.["platform-type"]
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.DIRECTOR,
        //         ROLE_CONSTANT.AREA_MANAGER,
        //         ROLE_CONSTANT.HR,
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER
        //     ]
        // );

        // Also check old permission system for backward compatibility
        const oldPermissionCheck = getUserCurrentRole && [
            ROLE_CONSTANT.SUPER_ADMIN,
            ROLE_CONSTANT.ADMIN,
            ROLE_CONSTANT.DIRECTOR,
            ROLE_CONSTANT.AREA_MANAGER,
            ROLE_CONSTANT.HR,
            ROLE_CONSTANT.BRANCH_MANAGER,
            ROLE_CONSTANT.HOTEL_MANAGER
        ].includes(getUserCurrentRole?.role_name);

        // User has permission if any check passes
        const hasPermission = checkModulePermission || /* checkAdminPermission || */ oldPermissionCheck;

        if (!hasPermission) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PERMISSION_DENIED"),
            });
        }

        const getAdminCheckList = await CheckList.findAll({
            attributes: [
                "id",
                "checkList_name",
                "checklist_type",
                "order",
                [
                    sequelize.literal(
                        `(IF((SELECT status FROM nv_user_leaving_checklist WHERE to_user_id = ${checkRequestExist?.user_id} AND from_user_id IS NOT NULL AND checklist_id = CheckList.id) = 'completed', true, false) )`,
                    ),
                    "leaving_checklist_status",
                ],
            ],
            where: {
                type: Sequelize.literal(
                    `(type & ${CHECKLISTTYPE.ADMIN}) = ${CHECKLISTTYPE.ADMIN}`,
                ),
                checklist_status: checklist_status.ACTIVE,
                checklist_type: checklist_type.LEAVING
            },
        });

        if (getAdminCheckList.length == 0) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("FAIL_CHECKLIST_NOT_FOUND") });
        }

        if (getAdminCheckList.length > 0) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: getAdminCheckList,
            });
        } else {
            return res
                .status(StatusCodes.BAD_REQUEST)
                .json({ status: false, message: res.__("FAIL_DATA_NOT_FOUND") });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

/**
 * Verify Leaving checklist 
 * @param req
 * @param res
 * @returns
 */

const verifyLeavingCheckList = async (req: any, res: Response) => {
    try {
        const { verified_checklist = [] } = req.body;
        const { resignation_id } = req.params;

        const checkRequestExist = await Resignation.findOne({
            where: {
                id: resignation_id,
                resignation_status: {
                    [Op.in]: [resign_status.ACCEPTED]
                }
            }, raw: true
        })

        if (!checkRequestExist) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_DATA_NOT_FOUND"),
            });
        }

        const getUserDetail: any = await User.findOne({
            where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
        });
        if (!getUserDetail) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
        }
        const getUserCurrentRole: any = await Role.findOne({ where: { id: req.headers["platform-type"] == "web" ? getUserDetail?.web_user_active_role_id : getUserDetail?.user_active_role_id, role_status: role_status.ACTIVE }, raw: true })
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'resignation', // Resignation module slug
            ROLE_PERMISSIONS.EDIT
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.DIRECTOR,
        //         ROLE_CONSTANT.AREA_MANAGER,
        //         ROLE_CONSTANT.HR,
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER
        //     ]
        // );

        // Also check old permission system for backward compatibility
        const oldPermissionCheck = getUserCurrentRole && [
            ROLE_CONSTANT.SUPER_ADMIN,
            ROLE_CONSTANT.ADMIN,
            ROLE_CONSTANT.DIRECTOR,
            ROLE_CONSTANT.AREA_MANAGER,
            ROLE_CONSTANT.HR,
            ROLE_CONSTANT.BRANCH_MANAGER,
            ROLE_CONSTANT.HOTEL_MANAGER
        ].includes(getUserCurrentRole?.role_name);

        // User has permission if any check passes
        const hasPermission = checkModulePermission || /* checkAdminPermission || */ oldPermissionCheck;

        if (!hasPermission) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PERMISSION_DENIED"),
            });
        }


        if (verified_checklist.length > 0) {

            _.forEach(verified_checklist, async (checklist: number) => {

                const checkAdminCheckList = await CheckList.findOne({
                    where: {
                        type: Sequelize.literal(
                            `(type & ${CHECKLISTTYPE.ADMIN}) = ${CHECKLISTTYPE.ADMIN}`,
                        ),
                        checklist_status: checklist_status.ACTIVE,
                        checklist_type: checklist_type.LEAVING,
                        id: checklist
                    }, raw: true
                });
                if (checkAdminCheckList) {
                    const findUserCheckList = await UserLeavingCheckList.findOne({
                        where: {
                            to_user_id: checkRequestExist.user_id,
                            from_user_id: { [Op.not]: checkRequestExist.user_id },
                            checklist_id: checklist,
                        }, raw: true
                    });
                    if (findUserCheckList) {
                        await UserLeavingCheckList.setHeaders(req).update(
                            { to_user_id: checkRequestExist.user_id, from_user_id: req.user.id, status: user_leaving_checklist_status.COMPLETED } as any,
                            {
                                where: {
                                    to_user_id: checkRequestExist.user_id,
                                    from_user_id: { [Op.not]: checkRequestExist.user_id },
                                    checklist_id: checklist,
                                },
                            },
                        );
                    } else {
                        await UserLeavingCheckList.setHeaders(req).create({
                            checklist_id: checklist,
                            to_user_id: checkRequestExist.user_id,
                            from_user_id: req.user.id,
                            status: user_leaving_checklist_status.COMPLETED,
                        } as any);
                    }
                }
            });

            await UserLeavingCheckList.setHeaders(req).update(
                {
                    to_user_id: checkRequestExist.user_id,
                    from_user_id: req.user.id,
                    status: user_leaving_checklist_status.PENDING,
                } as any,
                {
                    where: {
                        to_user_id: checkRequestExist.user_id,
                        from_user_id: { [Op.not]: checkRequestExist.user_id },
                        checklist_id: { [Op.not]: verified_checklist },
                    },
                },
            );

            return res
                .status(StatusCodes.OK)
                .json({ status: true, message: res.__("SUCCESS_ACCEPT_FORM_VERIFIED") });
        } else {
            await UserLeavingCheckList.destroy({
                where: { to_user_id: checkRequestExist.user_id, from_user_id: { [Op.not]: checkRequestExist.user_id } },
            });
            return res
                .status(StatusCodes.OK)
                .json({ status: true, message: res.__("SUCCESS_ACCEPT_FORM_VERIFIED") });
        }

    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

export default {
    sendResignation,
    getResignationList,
    getLeavingChecklist,
    getResignationById,
    updateResignationRequest,
    verifyLeavingCheckList
};
