import { Request, Response } from "express";
import { User, user_status } from "../models/User";
import {
  comparePassword,
  encrypt,
  generateOtp,
  otpExpTime,
} from "../helper/utils";
import { generateToken } from "../helper/auth.service";
import { Op, QueryTypes } from "sequelize";
import moment from "moment";
import { UserRole } from "../models/UserRole";
import { Role as MORole } from "../models/MORole";
import { MOPermission } from "../models/MOPermission";
import _ from "lodash";
import { checkUserProfileComplete, formatUserAgentData, getOrganizationLogo, getRoleName, getRoleNameEnhanced, otpAttemptValidation, sendEmailNotification } from "../helper/common";
import { StatusCodes } from "http-status-codes";
import { Activity, activity_action, activity_type } from "../models/Activity";
import {
  ADMIN_SIDE_USER,
  EMAIL_ADDRESS,
  EMAILCONSTANT,
  NORMAL_USER,
  ROLE_CONSTANT,
} from "../helper/constant";
import { emailSender } from "../helper/email.helper";
import { Role } from "../models/Role";
import { invitation_status, UserInvite } from "../models/UserInvite";
import { Workbook } from "exceljs";
import { DsrDetail } from "../models/DsrDetail";
import { DsrItem } from "../models/DsrItem";
import { WsrDetail } from "../models/WsrDetail";
import { WsrItem } from "../models/WsrItem";
import fs from "fs";
import { sequelize } from "../models";
import { expense_item_status, ExpenseItem } from "../models/ExpenseItem";
import { expense_detail_status, ExpenseDetail } from "../models/ExpenseDetail";
import axios from "axios";
import { user_weekday_status, UserWeekDay } from "../models/UserWeekDay";
import { ChangeRequest } from "../models/ChangeRequest";
import { ChangeRequestHistory } from "../models/ChangeRequestHistory";

/**
 *  User Login
 * @param req
 * @param res
 * @returns
 */

const login = async (req: Request, res: Response) => {
  try {
    const {
      user_email = "",
      user_password = "",
      webAppToken,
      appToken,
      login_type,
    } = req.body;
    const getUserDetail: any = await User.findOne({
      where: {
        user_email,
        user_status: {
          [Op.not]: [user_status.CANCELLED, user_status.DELETED],
        },
      },
      raw: true,
      nest: true,
    });

    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    } else {
      // Get user roles using MORole system if user_role_id exists, otherwise fallback to old system
      let loginUserRoles: string[] = [];
      let currentRole: any = null;

      if (getUserDetail.user_role_id) {
        // Use MORole system
        const moRole = await MORole.findOne({
          where: {
            id: getUserDetail.user_role_id,
            organization_id: getUserDetail.organization_id,
            role_status: 'active'
          },
          attributes: ["id", "role_name"],
          raw: true
        });

        if (moRole) {
          loginUserRoles = [moRole.role_name];
          currentRole = [{ role_name: moRole.role_name, id: moRole.id }];
        }
      } else {
        // Fallback to old UserRole system
        const userRoles = await UserRole.findAll({
          where: { user_id: getUserDetail.id },
          include: [
            {
              model: Role,
              as: "role",
              attributes: ["id", "role_name"],
            },
          ],
          nest: true,
          raw: true,
        });
        loginUserRoles = (userRoles.length > 0
          ? _.map(userRoles, (userRole: any) => userRole.role.role_name)
          : []);

        // Get current role for old system
        const getCurrentRole: any = await getRoleNameEnhanced(
          getUserDetail.web_user_active_role_id,
          getUserDetail.organization_id
        );
        const getAppCurrentRole: any = await getRoleNameEnhanced(
          getUserDetail.user_active_role_id,
          getUserDetail.organization_id
        );

        currentRole = req.headers["platform-type"] == "web" ? getCurrentRole : getAppCurrentRole;
      }

      const normalUser = [...NORMAL_USER];
      const adminSideUser = [...ADMIN_SIDE_USER];

      // Platform access validation using MORole permissions
      const platformType = req.headers["platform-type"] as string;

      if (getUserDetail.user_role_id) {
        // Use MORole system - check platform permissions at role level
        const platformValue = platformType === 'web' ? 1 : (platformType === 'ios' || platformType === 'android') ? 2 : 0;

        // Check if the role has access to the requested platform
        const userRole = await MORole.findOne({
          where: {
            id: getUserDetail.user_role_id,
            organization_id: getUserDetail.organization_id,
            role_status: 'active'
          },
          attributes: ['id', 'platform'],
          raw: true
        });

        if (!userRole) {
          return res
            .status(StatusCodes.FORBIDDEN)
            .json({ status: false, message: res.__("ROLE_NOT_FOUND") });
        }

        // Check if role platform allows access to requested platform
        const rolePlatform = userRole.platform;
        if (rolePlatform !== 3 && rolePlatform !== platformValue) {
          return res
            .status(StatusCodes.FORBIDDEN)
            .json({ status: false, message: res.__("PLATFORM_ACCESS_DENIED") });
        }

        // Check if user has any active permissions (at least one permission record)
        const hasAnyPermission = await MOPermission.findOne({
          where: {
            role_id: getUserDetail.user_role_id,
            organization_id: getUserDetail.organization_id,
            status: 'active'
          },
          raw: true
        });

        if (!hasAnyPermission) {
          return res
            .status(StatusCodes.FORBIDDEN)
            .json({ status: false, message: res.__("NO_PERMISSIONS_FOUND") });
        }
      } else {
        // Fallback to old role system for legacy users
        if (
          !loginUserRoles.some((item: any) => adminSideUser.includes(item)) &&
          platformType == "web"
        ) {
          return res
            .status(StatusCodes.FORBIDDEN)
            .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        if (
          !loginUserRoles.some((item: any) => normalUser.includes(item)) &&
          (platformType == "ios" || platformType == "android")
        ) {
          return res
            .status(StatusCodes.FORBIDDEN)
            .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }
      }
      // Prepare user role data for response
      let userRoleData: any = [];

      if (getUserDetail.user_role_id) {
        // Use MORole system
        userRoleData = [getUserDetail.user_role_id];
      } else {
        // Fallback to old UserRole system
        const getUserRole = await UserRole.findAll({
          attributes: ["role_id"],
          where: { user_id: getUserDetail.id },
          raw: true,
          nest: true,
        });
        userRoleData = _.map(getUserRole, (userRole: any) => userRole.role_id) || [];
      }

      const loginUserDetail: any = {
        user_id: getUserDetail.id,
        email: getUserDetail.user_email,
        is_login_pin: getUserDetail.is_login_pin,
        user_roles: userRoleData,
        user_status: getUserDetail.user_status,
        user_active_role: currentRole,
        updatedBy: getUserDetail.updated_by,
        profile_status: await checkUserProfileComplete(getUserDetail.id),
        device_type: req.headers?.["platform-type"],
      };

      if (
        login_type == "pin" &&
        getUserDetail.is_login_pin == true &&
        getUserDetail.user_login_pin
      ) {
        const isMatch = await comparePassword(
          user_password,
          getUserDetail.user_login_pin,
        );
        if (!isMatch) {
          await Activity.create({
            activity_table: "User",
            reference_id: getUserDetail.id,
            activity_type: activity_type.FAILED,
            activity_action: activity_action.LOGIN,
            ip_address: req.headers?.["ip-address"],
            userAgent: `${req.headers?.["platform-type"]} : ${formatUserAgentData(req.headers?.["user-agent"], req.headers?.["platform-type"])}`,
            address: req.headers?.["address"],
            location: req.headers?.["location"],
            organization_id: getUserDetail.organization_id
              ? getUserDetail.organization_id
              : null,
            created_by: getUserDetail.id,
            updated_by: getUserDetail.id,
          } as any);
          return res
            .status(StatusCodes.EXPECTATION_FAILED)
            .json({ status: false, message: res.__("ERROR_INCORRECT_PIN") });
        }
        const token = await generateToken({
          id: getUserDetail.id,
          email: getUserDetail.user_email,
          user_active_role_id: getUserDetail.user_active_role_id,
          web_user_active_role_id: getUserDetail.web_user_active_role_id,
          user_role_id: getUserDetail.user_role_id, // Add new MORole field
          user_roles: userRoleData,
          user_status: getUserDetail.user_status,
          user_active_role: currentRole,
          token_version: getUserDetail.token_version
            ? getUserDetail.token_version
            : 0,
          pin_token_version: getUserDetail.pin_token_version
            ? getUserDetail.pin_token_version
            : 0,
          device_type: req.headers?.["platform-type"],
        });
        loginUserDetail.token = token;
        req.user = getUserDetail;

        // await generateSession(req, res);
        // await generateRefreshToken(req, res);
      } else {
        const isMatch = await comparePassword(
          user_password,
          getUserDetail.user_password,
        );
        if (!isMatch) {
          await Activity.create({
            activity_table: "User",
            reference_id: getUserDetail.id,
            activity_type: activity_type.FAILED,
            ip_address: req.headers["ip-address"],
            activity_action: activity_action.LOGIN,
            userAgent: `${req.headers?.["platform-type"]} : ${formatUserAgentData(req.headers?.["user-agent"], req.headers?.["platform-type"])}`,
            location: req.headers?.["location"],
            address: req.headers?.["address"],
            organization_id: getUserDetail.organization_id
              ? getUserDetail.organization_id
              : null,
            created_by: getUserDetail.id,
            updated_by: getUserDetail.id,
          } as any);
          return res.status(StatusCodes.EXPECTATION_FAILED).json({
            status: false,
            message: res.__("ERROR_INCORRECT_EMAIL_PASSWORD"),
          });
        }
        const token = await generateToken({
          id: getUserDetail.id,
          email: getUserDetail.user_email,
          user_active_role_id: getUserDetail.user_active_role_id,
          web_user_active_role_id: getUserDetail.web_user_active_role_id,
          user_role_id: getUserDetail.user_role_id,
          token_version: getUserDetail.token_version
            ? getUserDetail.token_version
            : 0,
          pin_token_version: getUserDetail.pin_token_version
            ? getUserDetail.pin_token_version
            : 0,
          device_type: req.headers?.["platform-type"],
        });

        loginUserDetail.token = token;
        req.user = getUserDetail;
        // await generateSession(req, res);
        // await generateRefreshToken(req, res);
      }

      await Activity.create({
        activity_table: "User",
        reference_id: getUserDetail.id,
        activity_type: activity_type.SUCCESS,
        activity_action: activity_action.LOGIN,
        ip_address: req.headers["ip-address"],
        userAgent: `${req.headers?.["platform-type"]} : ${formatUserAgentData(req.headers?.["user-agent"], req.headers?.["platform-type"])}`,
        location: req.headers?.["location"],
        address: req.headers?.["address"],
        organization_id: getUserDetail.organization_id
          ? getUserDetail.organization_id
          : null,
        created_by: getUserDetail.id,
        updated_by: getUserDetail.id,
      } as any);
      if (webAppToken || appToken) {
        webAppToken
          ? await User.setHeaders(req).update(
            { webAppToken: webAppToken },
            { where: { user_email: user_email } },
          )
          : null;
        appToken
          ? await User.setHeaders(req).update(
            { appToken: appToken },
            { where: { user_email: user_email } },
          )
          : null;
        const updateWebAppToken: any = null;
        webAppToken
          ? await User.setHeaders(req).update(
            { webAppToken: updateWebAppToken },
            {
              where: {
                webAppToken: webAppToken,
                user_email: { [Op.not]: user_email },
              },
            },
          )
          : false;
        appToken
          ? await User.setHeaders(req).update(
            { appToken: updateWebAppToken },
            {
              where: {
                appToken: appToken,
                user_email: { [Op.not]: user_email },
              },
            },
          )
          : false;
      }
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_LOGIN"),
        user_data: loginUserDetail,
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Forgot password
 * @param req
 * @param res
 * @returns
 */

const forgotPassword = async (req: Request, res: Response) => {
  try {
    const { user_email = "", forgot_type } = req.body;
    const otp = await generateOtp(4);
    const CheckEmailExist: any = await User.findOne({
      where: {
        user_email,
        user_status: {
          [Op.not]: [user_status.CANCELLED, user_status.DELETED],
        },
      },
      raw: true,
    });
    if (!CheckEmailExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    } else {
      req.user = CheckEmailExist;

      // Get current role using MORole system if available, otherwise fallback to old system
      let currentRole: any = null;

      if (CheckEmailExist.user_role_id) {
        // Use MORole system
        const moRole = await MORole.findOne({
          where: {
            id: CheckEmailExist.user_role_id,
            organization_id: CheckEmailExist.organization_id,
            role_status: 'active'
          },
          attributes: ["id", "role_name"],
          raw: true
        });

        if (moRole) {
          currentRole = [{ role_name: moRole.role_name, id: moRole.id }];
        }
      } else {
        // Fallback to old system
        currentRole = await getRoleNameEnhanced(
          req.headers["platform-type"] == "web"
            ? CheckEmailExist.web_user_active_role_id
            : CheckEmailExist.user_active_role_id,
          CheckEmailExist.organization_id
        );
      }

      if (
        CheckEmailExist.user_status == user_status.PENDING &&
        currentRole && currentRole[0] &&
        currentRole[0].role_name != ROLE_CONSTANT.ADMIN &&
        currentRole[0].role_name != ROLE_CONSTANT.SUPER_ADMIN
      ) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("ERROR_PLEASE_VERIFY_ACCOUNT"),
        });
      }

      if (
        forgot_type &&
        forgot_type == "pin" &&
        !CheckEmailExist.is_login_pin
      ) {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("FAIL_PLEASE_SET_PIN") });
      }
      if (forgot_type && forgot_type == "pin") {
        const attemptValidation = await otpAttemptValidation(
          CheckEmailExist.user_email,
          EMAILCONSTANT.FORGOT_PIN_OTP.subject,
          global.config.FORGOT_PASSWORD_ATTEMPT,
        );
        if (attemptValidation != true) {
          return res
            .status(StatusCodes.TOO_MANY_REQUESTS)
            .json({ status: false, message: res.__(attemptValidation) });
        }
      } else {
        const attemptValidation = await otpAttemptValidation(
          CheckEmailExist.user_email,
          EMAILCONSTANT.FORGOT_PASSWORD_OTP.subject,
          global.config.FORGOT_PASSWORD_ATTEMPT,
        );
        if (attemptValidation != true) {
          return res
            .status(StatusCodes.TOO_MANY_REQUESTS)
            .json({ status: false, message: res.__(attemptValidation) });
        }
      }
      await User.setHeaders(req).update(
        { otp: otp, otp_expire: await otpExpTime() },
        { where: { id: CheckEmailExist.id } },
      );
      const templateData: any = {
        name: `${CheckEmailExist.user_first_name} ${CheckEmailExist.user_last_name}`,
        email: CheckEmailExist.user_email,
        otp: otp,
        ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
        LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
        ADDRESS: EMAIL_ADDRESS.ADDRESS,
        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
        EMAIL: EMAIL_ADDRESS.EMAIL,
        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
        smtpConfig: 'INFO'
      };

      if (forgot_type && forgot_type == 'pin') {
        templateData.mail_type = 'forgot_pin'
        await sendEmailNotification(templateData)

        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("SUCCESS_FORGOT_OTP_PIN"),
        });
      } else {
        templateData.mail_type = 'forgot_password'
        await sendEmailNotification(templateData)
        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("SUCCESS_FORGOT_OTP"),
        });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Forgot password Verify
 * @param req
 * @param res
 * @returns
 */

const forgotPasswordVerify = async (req: Request, res: Response) => {
  try {
    const { user_email = "", new_password, type } = req.body;

    const CheckEmailExist: any = await User.findOne({
      where: {
        user_email,
        user_status: {
          [Op.not]: [user_status.CANCELLED, user_status.DELETED],
        },
      },
      raw: true,
    });
    if (!CheckEmailExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    req.user = CheckEmailExist;
    const isMatch = await comparePassword(
      new_password,
      CheckEmailExist.user_password,
    );
    if (isMatch) {
      return res.status(StatusCodes.NOT_ACCEPTABLE).json({
        status: false,
        message: res.__("FAIL_NEW_PASSWORD_MUST_DIFFERENT"),
      });
    }
    const updateObj: any = {
      user_password: await encrypt(new_password),
      // token_version :(CheckEmailExist.token_version + 1) % 256 ,
      updated_by: CheckEmailExist.id,
    };
    if (CheckEmailExist.user_status == user_status.PENDING) {
      if (type == "change") {
        updateObj.user_status = user_status.ACTIVE;
        const findUserInvitation = await UserInvite.findOne({
          where: {
            user_id: CheckEmailExist.id,
            invitation_status: { [Op.not]: invitation_status.ACCEPTED },
          },
        });
        if (findUserInvitation) {
          await UserInvite.update(
            { invitation_status: invitation_status.ACCEPTED },
            { where: { id: findUserInvitation.id } },
          );
        }
      }
    }

    await User.setHeaders(req).update(updateObj, { where: { id: CheckEmailExist.id } });
    const templateData: any = {
      name: CheckEmailExist.user_first_name,
      email: CheckEmailExist.user_email,
      mail_type: 'reset_password_success',
      ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
      LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
      ADDRESS: EMAIL_ADDRESS.ADDRESS,
      PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
      EMAIL: EMAIL_ADDRESS.EMAIL,
      ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
      smtpConfig: 'INFO'
    };
    await sendEmailNotification(templateData);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_RESET_PASSWORD"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Forgot pin
 * @param req
 * @param res
 * @returns
 */

const forgotPin = async (req: Request, res: Response) => {
  try {
    const { user_email = "", user_password = "", new_pin } = req.body;

    const CheckEmailExist: any = await User.findOne({
      where: {
        user_email,
        user_status: {
          [Op.not]: [user_status.CANCELLED, user_status.DELETED],
        },
      },
      raw: true,
    });
    if (!CheckEmailExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    req.user = CheckEmailExist;
    if (CheckEmailExist.user_login_pin !== undefined) {
      const verifyPassword = await comparePassword(
        user_password,
        CheckEmailExist.user_password,
      );
      if (!verifyPassword) {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("PASSWORD_WRONG") });
      }

      const isMatch = await comparePassword(
        new_pin,
        CheckEmailExist.user_login_pin,
      );
      if (isMatch) {
        return res.status(StatusCodes.NOT_ACCEPTABLE).json({
          status: false,
          message: res.__("ERROR_NEW_PIN_MUST_DIFFERENT"),
        });
      }

      await User.setHeaders(req).update(
        { user_login_pin: await encrypt(new_pin) },
        { where: { id: CheckEmailExist.id } },
      );
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_RESET_PIN"),
      });
    } else {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_PLEASE_SET_PIN") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Verify Otp
 * @param req
 * @param res
 * @returns
 */

const verifyOtp = async (req: Request, res: Response) => {
  try {
    const { user_email = "", user_otp = "" } = req.body;

    const CheckEmailExist: any = await User.findOne({
      where: {
        user_email,
        user_status: {
          [Op.not]: [user_status.CANCELLED, user_status.DELETED],
        },
      },
      raw: true,
    });
    if (!CheckEmailExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }

    if (moment() > CheckEmailExist.otp_expire) {
      return res
        .status(StatusCodes.NOT_ACCEPTABLE)
        .json({ status: false, message: res.__("OTP_EXPIRE") });
    }

    if (user_otp == CheckEmailExist.otp) {
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("SUCCESS_OTP_VERIFIED") });
    } else {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("OTP_NOT_MATCH") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * resend Otp
 * @param req
 * @param res
 * @returns
 */

const resendOtp = async (req: Request, res: Response) => {
  try {
    const { user_email = "" } = req.body;
    const otp = await generateOtp(4);
    const CheckEmailExist: any = await User.findOne({
      where: {
        user_email,
        user_status: {
          [Op.not]: [user_status.CANCELLED, user_status.DELETED],
        },
      },
      raw: true,
    });
    if (!CheckEmailExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    } else {
      const attemptValidation = await otpAttemptValidation(
        CheckEmailExist.user_email,
        EMAILCONSTANT.RESEND_OTP.subject,
        global.config.RESEND_OTP_ATTEMPT,
      );
      if (attemptValidation != true) {
        return res
          .status(StatusCodes.TOO_MANY_REQUESTS)
          .json({ status: false, message: res.__(attemptValidation) });
      }
      req.user = CheckEmailExist;
      const updateUserOtp = await User.setHeaders(req).update(
        { otp: otp, otp_expire: await otpExpTime() },
        { where: { id: CheckEmailExist.id } },
      );
      if (updateUserOtp.length > 0) {
        const templateData = {
          name: `${CheckEmailExist.user_first_name} ${CheckEmailExist.user_last_name}`,
          email: CheckEmailExist.user_email,
          otp: otp,
        };

        emailSender(
          user_email,
          EMAILCONSTANT.RESEND_OTP.subject,
          templateData,
          EMAILCONSTANT.RESEND_OTP.template,
        );

        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("SUCCESS_OTP_SENT"),
          otp: otp,
        });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: false, message: res.__("FAIL_OTP_RESEND") });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const dsrDataEntry = async (req: any, res: Response) => {
  try {
    const { sheet_array = [], column_count } = req.body;
    const filePath = req?.file?.path;
    // Create a new workbook instance
    const workbook = new Workbook();

    // Read the Excel file
    await workbook.xlsx.readFile(filePath);
    const alreadyExistDsr: any = [];
    const addedDsr: any = [];
    if (sheet_array.length > 0) {
      for (const sheet of sheet_array) {
        // Select the desired worksheet by name
        const worksheet: any = workbook.getWorksheet(sheet);
        const branch_id = worksheet?.getCell("B3").value;
        // Initialize an array to store results
        const excelData: Array<Record<string, any>> = [];

        // Iterate through rows starting from row 5
        worksheet.eachRow(
          { includeEmpty: true },
          (row: any, rowNumber: number) => {
            if (rowNumber >= 5) {
              // Process rows starting from row 5
              // Initialize the result object for this row
              const rowData: Record<string, any> = {
                date: worksheet.getCell(rowNumber, 1).value, // Get value from column A
                dsr_total_obj: {},
                category_data: [],
              };

              // Initialize an object to store dsr_total_obj data (for string type columns)
              const dsrData: Record<string, any> = {};

              // Loop through columns starting from C (3rd column)
              let isValid = false; // Flag to check if the row contains valid data
              for (let colNumber = 3; colNumber <= column_count; colNumber++) {
                const headerCell = worksheet.getCell(3, colNumber); // Get the cell from row 3 for the current column
                const headerValue = headerCell.value;
                // Check if the type of the header is not a string (to avoid string types in headers)
                if (typeof headerValue !== "string") {
                  // Add an object with `id` from row 3 and `value` from the current cell

                  if (typeof row.getCell(colNumber).value === "object") {
                    rowData.category_data.push({
                      id: headerValue,
                      value: row.getCell(colNumber).value?.result,
                    });
                  } else {
                    rowData.category_data.push({
                      id: headerValue,
                      value: row.getCell(colNumber).value,
                    });
                  }
                }
                // If the header is a string (valid column for data)
                if (typeof headerValue === "string") {
                  isValid = true;
                  // Add the data to dsr_total_obj (as stringified key-value)
                  if (typeof row.getCell(colNumber).value === "object") {
                    dsrData[headerValue] = row.getCell(colNumber).value?.result;
                  } else {
                    dsrData[headerValue] = row.getCell(colNumber).value;
                  }
                }
              }
              // Assign the dsrData object to dsr_total_obj
              rowData.dsr_total_obj = JSON.stringify(dsrData);

              // If valid data is found for this row, add it to the result array
              if (isValid) {
                excelData.push(rowData);
              }
            }
          },
        );

        if (excelData.length > 0) {
          for (const data of excelData) {
            if (data.date) {
              const findDsrExist = await DsrDetail.findOne({
                where: {
                  dsr_date: moment(data.date).format("YYYY-MM-DD"),
                  dsr_detail_status: "active",
                  branch_id: branch_id?.result,
                },
              });
              if (!findDsrExist) {
                const addDsrDetail = await DsrDetail.create({
                  user_id: 1,
                  branch_id: branch_id.result,
                  dsr_date: moment(data.date).format("YYYY-MM-DD"),
                  dsr_detail_status: "active",
                  dsr_amount_total: data.dsr_total_obj,
                  created_by: 1,
                  updated_by: 1,
                } as any);
                if (addDsrDetail) {
                  addedDsr.push({
                    id: addDsrDetail.id,
                    dsr_date: addDsrDetail.dsr_date,
                  });
                  if (data?.category_data.length > 0) {
                    for (const category of data.category_data) {
                      await DsrItem.create({
                        dsr_detail_id: addDsrDetail.id,
                        dsr_amount: category.value,
                        dsr_item_status: "active",
                        payment_type_category_id: category?.id?.result,
                        created_by: 1,
                        updated_by: 1,
                      } as any);
                    }
                  }
                }
              } else {
                alreadyExistDsr.push({
                  id: findDsrExist.id,
                  dsr_date: findDsrExist.dsr_date,
                });
              }
            }
          }
        }

        // Log the result
        return res.send({ exist: alreadyExistDsr, added: addedDsr });
      }
    }
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath); // Delete the file
    }
    return res.send({ exist: alreadyExistDsr, added: addedDsr });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};
const wsrDataEntry = async (req: any, res: Response) => {
  try {
    const { sheet_array = [], column_count } = req.body;
    const filePath = req?.file?.path;
    // Create a new workbook instance
    const workbook = new Workbook();

    // Read the Excel file
    await workbook.xlsx.readFile(req?.file?.path);
    const alreadyExistDsr: any = [];
    if (sheet_array.length > 0) {
      for (const sheet of sheet_array) {
        // Select the desired worksheet by name
        const worksheet: any = workbook.getWorksheet(sheet);
        const branch_id = worksheet?.getCell("B3").value;
        // Initialize an array to store results
        const excelData: Array<Record<string, any>> = [];

        // Iterate through rows starting from row 5
        worksheet.eachRow(
          { includeEmpty: true },
          (row: any, rowNumber: number) => {
            if (rowNumber >= 5) {
              // Process rows starting from row 5
              // Initialize the result object for this row
              const rowData: Record<string, any> = {
                start_date: worksheet.getCell(rowNumber, 1).value, // Get value from column A
                end_date: worksheet.getCell(rowNumber, 2).value,
                dsr_total_obj: {},
                category_data: [],
              };

              // Initialize an object to store dsr_total_obj data (for string type columns)
              const dsrData: Record<string, any> = {};

              // Loop through columns starting from C (3rd column)
              let isValid = false; // Flag to check if the row contains valid data
              for (let colNumber = 3; colNumber <= column_count; colNumber++) {
                const headerCell = worksheet.getCell(3, colNumber); // Get the cell from row 3 for the current column
                const headerValue = headerCell.value;
                // Check if the type of the header is not a string (to avoid string types in headers)
                if (typeof headerValue !== "string") {
                  // Add an object with `id` from row 3 and `value` from the current cell
                  rowData.category_data.push({
                    id: headerValue,
                    value: row.getCell(colNumber).value,
                  });
                }
                // If the header is a string (valid column for data)
                if (typeof headerValue === "string") {
                  isValid = true;
                  // Add the data to dsr_total_obj (as stringified key-value)
                  if (typeof row.getCell(colNumber).value === "object") {
                    dsrData[headerValue] = row.getCell(colNumber).value?.result;
                  } else {
                    dsrData[headerValue] = row.getCell(colNumber).value;
                  }
                }
              }

              // Assign the dsrData object to dsr_total_obj
              rowData.wsr_total_obj = JSON.stringify(dsrData);

              // If valid data is found for this row, add it to the result array
              if (isValid) {
                excelData.push(rowData);
              }
            }
          },
        );

        if (excelData.length > 0) {
          for (const data of excelData) {
            if (data.start_date && data.end_date) {
              const findWsrExist = await WsrDetail.findOne({
                where: {
                  wsr_start_date: moment(data.start_date).format("YYYY-MM-DD"),
                  wsr_end_date: moment(data.end_date).format("YYYY-MM-DD"),
                  wsr_detail_status: "active",
                  branch_id: branch_id,
                },
              });
              if (!findWsrExist) {
                const addWsrDetail = await WsrDetail.create({
                  user_id: 1,
                  branch_id: branch_id,
                  wsr_start_date: moment(data.start_date).format("YYYY-MM-DD"),
                  wsr_end_date: moment(data.end_date).format("YYYY-MM-DD"),
                  wsr_detail_status: "active",
                  wsr_amount_total: data.wsr_total_obj,
                  created_by: 1,
                  updated_by: 1,
                } as any);
                if (addWsrDetail) {
                  if (data?.category_data.length > 0) {
                    for (const category of data.category_data) {
                      await WsrItem.create({
                        wsr_detail_id: addWsrDetail.id,
                        wsr_amount: category.value,
                        wsr_item_status: "active",
                        payment_type_category_id: category.id,
                        created_by: 1,
                        updated_by: 1,
                      } as any);
                    }
                  }
                }
              } else {
                alreadyExistDsr.push({
                  id: findWsrExist.id,
                  wsr_start_date: findWsrExist.wsr_start_date,
                  wsr_end_date: findWsrExist.wsr_end_date,
                });
              }
            }
          }
        }
      }
    }
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath); // Delete the file
    }
    return res.send(alreadyExistDsr);
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};
const removeVAT = (totalAmount: any, vatPercentage: any) => {
  const vatDecimal = vatPercentage / 100;

  const netAmount = totalAmount / (1 + vatDecimal);

  const vatAmount = totalAmount - netAmount;
  return {
    netAmount: parseFloat(netAmount.toFixed(2)), // AmountVAT2 (without VAT)
    vatAmount: parseFloat(vatAmount.toFixed(2)), // AmountVAT1
  };
};
const vatDataCalculation = async (req: any, res: Response) => {
  try {
    const getReportQuery = `SELECT *, 
    (SELECT SUM(dsr_amount) FROM nv_dsr_items WHERE dsr_detail_id = nv_dsr_details.id AND dsr_item_status = 'active' AND (SELECT (SELECT has_include_amount FROM nv_payment_type WHERE id = nv_payment_type_category.payment_type_id AND payment_type_usage = 'income' AND has_weekly_use = 0) AS has_include_amount FROM nv_payment_type_category WHERE id = nv_dsr_items.payment_type_category_id)) AS TotalIncome 
    FROM nv_dsr_details
    WHERE dsr_detail_status = 'active';`;
    const getReportData: any = await sequelize.query(getReportQuery, {
      type: QueryTypes.SELECT,
    });

    for (const data of getReportData) {
      const dsrAmountTotal = JSON.parse(data.dsr_amount_total);
      if (
        typeof dsrAmountTotal.TotalIncome === "undefined" ||
        !dsrAmountTotal.TotalIncome
      ) {
        dsrAmountTotal.TotalIncome = data.TotalIncome
          ? data.TotalIncome.toFixed(2)
          : "0";
      }

      const removeVATData: any = removeVAT(parseFloat(dsrAmountTotal.VAT2), 20);
      data.removeVATData = removeVATData;
      dsrAmountTotal.AmountVAT2 = (
        removeVATData.netAmount +
        parseFloat(dsrAmountTotal.VAT1) +
        parseFloat(dsrAmountTotal.NoneVat)
      ).toFixed(2);

      dsrAmountTotal.AmountVAT1 = removeVATData.vatAmount.toFixed(2);

      if (
        typeof dsrAmountTotal.diff1 === "undefined" ||
        !dsrAmountTotal.diff1
      ) {
        dsrAmountTotal.diff1 = "0";
      }

      if (
        typeof dsrAmountTotal.diff2 === "undefined" ||
        !dsrAmountTotal.diff2
      ) {
        const diff1 =
          typeof dsrAmountTotal.diff1 !== "undefined" && dsrAmountTotal.diff2
            ? dsrAmountTotal.diff1
            : 0;
        const diff2 = Math.abs(parseFloat(dsrAmountTotal.AmountVAT1) - diff1);
        dsrAmountTotal.diff2 = diff2.toFixed(2);
      }

      // data.dsr_amount_total1 = JSON.stringify(dsrAmountTotal)
      await DsrDetail.update(
        { dsr_amount_total: JSON.stringify(dsrAmountTotal) },
        { where: { id: data.id } },
      );
    }

    return res.send(getReportData);
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const expenseDataEntry = async (req: any, res: Response) => {
  try {
    const { sheet_array = [], branch_id } = req.body;
    const filePath = req?.file?.path;

    const workbook = new Workbook();
    await workbook.xlsx.readFile(filePath);

    const alreadyExistExpense: any = [];
    const processedMonths: any = [];
    const expenseItemData: any = [];

    if (sheet_array.length > 0) {
      for (const sheet of sheet_array) {
        const worksheet: any = workbook.getWorksheet(sheet);
        const monthYears: Record<number, { month: number; year: number }> = {};

        // Extract month-year headers
        for (let col = 3; col <= worksheet.columnCount; col++) {
          const headerCell = worksheet.getCell(2, col);
          if (headerCell && headerCell.value) {
            const cellDate = moment(headerCell.value, "MMM-YY", true);
            if (cellDate.isValid()) {
              monthYears[col] = {
                month: cellDate.month() + 1,
                year: cellDate.year(),
              };
            }
          }
        }

        for (const [col, { month, year }] of Object.entries(monthYears)) {
          const colNumber = parseInt(col);
          let hasNonEmptyCategory = false; // Flag to check if any non-empty value exists

          for (
            let rowNumber = 4;
            rowNumber <= worksheet.rowCount;
            rowNumber++
          ) {
            const value = worksheet.getCell(rowNumber, colNumber).value;
            if (typeof value === "number" && !isNaN(value)) {
              hasNonEmptyCategory = true;
              break; // No need to check further, we found a valid entry
            }
          }

          if (!hasNonEmptyCategory) {
            console.log(
              `Skipping expense creation for ${month}/${year} as all values are empty`,
            );
            continue; // Skip this month-year if all values are empty
          }

          // Check if expense already exists
          const findExpenseExist = await ExpenseDetail.findOne({
            where: {
              expense_month: month,
              expense_year: year,
              branch_id: branch_id,
              expense_detail_status: expense_detail_status.ACTIVE,
            },
          });

          if (findExpenseExist) {
            alreadyExistExpense.push({
              id: findExpenseExist.id,
              expense_month: month,
              expense_year: year,
            });
            continue;
          }

          // Create the expense detail entry
          const addExpenseDetail = await ExpenseDetail.create({
            user_id: 1,
            branch_id: branch_id,
            expense_month: month,
            expense_year: year,
            expense_detail_status: expense_detail_status.ACTIVE,
            created_by: 1,
            updated_by: 1,
          } as any);

          // Process all rows for this month-year
          for (
            let rowNumber = 3;
            rowNumber <= worksheet.rowCount;
            rowNumber++
          ) {
            const mainCategory = worksheet.getCell(rowNumber, 1).value;
            const subCategory = worksheet.getCell(rowNumber, 3).value;
            let value = worksheet.getCell(rowNumber, colNumber).value;

            if (!mainCategory || !subCategory) continue;
            value = value?.result ? value.result : value;
            if (typeof value !== "number" || isNaN(value)) {
              value = 0; // If value is missing, set it to 0
            }
            await ExpenseItem.create({
              expense_detail_id: addExpenseDetail.id,
              payment_type_category_id: subCategory.result,
              expense_amount: parseFloat(value.toFixed(2)),
              expense_item_status: expense_item_status.ACTIVE,
              created_by: 1,
              updated_by: 1,
            } as any);
          }

          processedMonths.push({
            month,
            year,
            expense_id: addExpenseDetail.id,
          });
        }
      }
    }

    // Delete file after processing
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("EXPENSE_DATA_PROCESSED_SUCCESSFULLY"),
      data: {
        processed: processedMonths,
        already_exist: alreadyExistExpense,
        expenseItemData: expenseItemData,
      },
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const oldDataUserScript = async (req: any, res: Response) => {
  try {
    const getAllUsers = await User.findAll({
      attributes: ['id', 'user_first_name', 'user_last_name', 'user_email', 'user_phone_number', 'is_login_pin', 'organization_id'],
      where: { keycloak_auth_id: { [Op.eq]: null } } as any,
      raw: true
    })
    console.log("Test Log>>>>>>>>>>>>>>>>", getAllUsers.length)
    for (let i = 0; i < getAllUsers.length; i++) {
      try {
        const apiUrl = global.config.AUTH_API_URL; // Replace with your API URL
        const headers = {
          'Content-Type': 'application/json'
        };
        console.log("Test Log>>>>>>>>>>>>>>>>", getAllUsers[i])
        // getAllUsers[i].user_password = await GeneratePassword()
        await axios.post(apiUrl, getAllUsers[i], { headers });
      } catch (error: any) {
        console.error('Error calling API:', error.response?.data || error.message);
      }
    }
    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("SUCCESS_USER_CREATED"),
      data: null
    })
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
}

const checkUsernameAvailability = async (req: any, res: any) => {
  try {
    const { username } = req.query;

    if (!username) {
      return res.status(400).json({ message: "Username query is required." });
    }

    // Check if a user with the given username exists
    const existingUser = await User.findOne({
      where: { username },
      attributes: ['id'],
      raw: true
    });
    if (existingUser) {
      return res.json({ available: false, message: "Username already taken." });
    } else {
      return res.json({ available: true, message: "Username available." });
    }
  } catch (error) {
    console.error("Error checking username:", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/** Script for user week day entry data */
const userWeekDayEntry = async (req: any, res: any) => {
  try {
    const getAllUsers = await User.findAll({
      attributes: ['id'],
      raw: true
    })
    for (let i = 0; i < getAllUsers.length; i++) {
      const findUserWeekDay = await UserWeekDay.findOne({
        where: { user_id: getAllUsers[i].id },
        raw: true
      })
      if (!findUserWeekDay) {
        await UserWeekDay.create({
          user_id: getAllUsers[i].id,
          user_weekday_status: user_weekday_status.ACTIVE,
          created_by: 1,
          updated_by: 1,
        } as any)
      }
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_USER_WEEK_DAY_ENTRY"),
      data: null
    })
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
}

/** Old user migration script */
const oldUserChangeRequestMigration = async (req: any, res: any) => {
  try {
    const getAllUsers = await User.findAll({
      attributes: ['id'],
      raw: true
    });

    for (const user of getAllUsers) {
      const changeRequests = await ChangeRequest.findAll({
        where: { user_id: user.id },
        raw: true
      });

      for (const cr of changeRequests) {
        let updated = false;
        const updatedFields: any = {};

        // OLD DATA
        if (typeof cr.old_data === 'string' && !cr.old_data.trim().startsWith('{')) {
          updatedFields.old_data = JSON.stringify({ data: cr.old_data.trim() });
          updated = true;
        }

        // NEW DATA
        if (typeof cr.new_data === 'string' && !cr.new_data.trim().startsWith('{')) {
          updatedFields.new_data = JSON.stringify({ data: cr.new_data.trim() });
          updated = true;
        }
        // Update only if something was changed
        if (updated) {
          await ChangeRequest.update(updatedFields, {
            where: { id: cr.id }
          });
        }

        if (updated) {
          await ChangeRequestHistory.update(updatedFields, {
            where: { change_request_id: cr.id }
          });
        }
      }
    }

    return res.status(200).json({
      status: true,
      message: "Migration completed successfully"
    });

  } catch (error) {
    console.error("Migration error:", error);
    return res.status(503).json({
      status: false,
      message: "Something went wrong",
      data: error
    });
  }
};



export default {
  login,
  forgotPassword,
  forgotPasswordVerify,
  forgotPin,
  verifyOtp,
  resendOtp,
  dsrDataEntry,
  wsrDataEntry,
  vatDataCalculation,
  expenseDataEntry,
  oldDataUserScript,
  checkUsernameAvailability,
  userWeekDayEntry,
  oldUserChangeRequestMigration
};
