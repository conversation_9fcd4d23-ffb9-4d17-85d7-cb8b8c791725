import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { User, user_status } from "../models/User";
import { Op } from "sequelize";
import { StatusCodes } from "http-status-codes";
import { ADMIN_SIDE_USER, NORMAL_USER, ROLE_CONSTANT } from "../helper/constant";
import { UserRole } from "../models/UserRole";
import { Role } from "../models/Role";
import { Role as MORole } from "../models/MORole";
import { MOPermission } from "../models/MOPermission";
import _ from "lodash";
import { UserSession } from "../models/UserSession";

/**
 * Check if user has platform access based on M<PERSON><PERSON> permissions
 * @param userId - User ID
 * @param organizationId - Organization ID
 * @param platformType - Platform type (web, ios, android)
 * @returns boolean - true if user has access to the platform
 */
const checkPlatformAccess = async (userId: number, organizationId: string, platformType: string): Promise<boolean> => {
  try {
    // Get user's role
    const user = await User.findOne({
      where: { id: userId },
      attributes: ['user_role_id'],
      raw: true
    });

    if (!user || !user.user_role_id) {
      return false; // No MORole assigned
    }

    // Check if user has any permissions for the requested platform
    const platformValue = platformType === 'web' ? 1 : (platformType === 'ios' || platformType === 'android') ? 2 : 0;

    // Query permissions for the user's role and platform
    const hasPermission = await MOPermission.findOne({
      where: {
        role_id: user.user_role_id,
        organization_id: organizationId,
        platform: {
          [Op.in]: [platformValue, 3] // Platform-specific (1 or 2) or both (3)
        },
        status: 'active'
      },
      raw: true
    });

    return !!hasPermission;
  } catch (error) {
    console.log('Error checking platform access:', error);
    return false;
  }
};

const userAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // checking if token is present in header
    if (!req.headers.authorization) {
      return res
        .status(403)
        .send({ status: false, message: res.__("ERR_TOKEN_NOT_FOUND") });
    }
    // if (!req.cookies) {
    //   res.status(403).send({ message: "Unauthorized request" });
    //   return;
    // }

    // const sessionToken: string = req.cookies["refresh_token"];

    // if (!sessionToken) {
    //   res.status(403).send({ message: "Unauthorized request" });
    //   return;
    // }

    // const refreshVerify = jwt.verify(
    //   sessionToken,
    //   global.config.REFRESH_TOKEN_SECRET,
    // );
    // if (!refreshVerify) {
    //   return res.status(403).send({ message: "Unauthorized request" });
    // }
    // if (!refreshVerify.id) {
    //   return res.status(403).send({ message: "Unauthorized request" });
    // }
    // removing Bearer keyword
    const token: string = req.headers.authorization?.replace("Bearer ", "");

    // verifying token
    const decoded = jwt.verify(token, global.config.JWT_SECRET);

    // if (refreshVerify.id != decoded.id) {
    //   return res.status(403).send({ message: "Unauthorized request" });
    // }
    // finding user with details decoded fro token
    const getUserDetail: any = await User.findOne({
      where: {
        // id: decoded.id,
        keycloak_auth_id: decoded.user_id,
        user_status: {
          [Op.not]: [
            user_status.CANCELLED,
            user_status.DELETED
          ],
        },
      },
      raw: true,
      nest: true,
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    // Get user roles using MORole system if user_role_id exists, otherwise fallback to old system
    let loginUserRoles: string[] = [];

    if (getUserDetail.user_role_id) {
      // Use MORole system
      const moRole = await MORole.findOne({
        where: {
          id: getUserDetail.user_role_id,
          organization_id: getUserDetail.organization_id,
          role_status: 'active'
        },
        attributes: ["id", "role_name"],
        raw: true
      });

      if (moRole) {
        loginUserRoles = [moRole.role_name];
      }
    } else {
      // Fallback to old UserRole system
      const userRoles = await UserRole.findAll({
        where: { user_id: getUserDetail.id },
        include: [
          {
            model: Role,
            as: "role",
            attributes: ["id", "role_name"],
          },
        ],
        nest: true,
        raw: true,
      });
      loginUserRoles = (userRoles.length > 0
        ? _.map(userRoles, (userRole: any) => userRole.role.role_name)
        : []);
    }

    // Platform access validation using MORole permissions
    const platformType = req.headers["platform-type"] as string;

    if (getUserDetail.user_role_id && platformType == "web") {
      // Use MORole system - check platform permissions dynamically
      const hasAccess = getUserDetail.organization_id ?  await checkPlatformAccess(
        getUserDetail.id,
        getUserDetail.organization_id,
        platformType
      ) : true;

      if (!hasAccess) {
        return res
          .status(StatusCodes.UNAUTHORIZED)
          .json({ status: false, message: res.__("FAIL_TOKEN_EXPIRED") });
      }
    } else {
      // Fallback to old role system for legacy users
      const normalUser = [...NORMAL_USER];
      const adminSideUser = [...ADMIN_SIDE_USER];

      if (
        !loginUserRoles.some((item: any) => adminSideUser.includes(item)) &&
        platformType == "web"
      ) {
        return res
          .status(StatusCodes.UNAUTHORIZED)
          .json({ status: false, message: res.__("FAIL_TOKEN_EXPIRED") });
      }

      if (
        !loginUserRoles.some((item: any) => normalUser.includes(item)) &&
        (platformType == "ios" || platformType == "android")
      ) {
        return res
          .status(StatusCodes.UNAUTHORIZED)
          .json({ status: false, message: res.__("FAIL_TOKEN_EXPIRED") });
      }
    }
    // Check for Super Admin role for session validation
    const isSuperAdmin = loginUserRoles.includes(ROLE_CONSTANT.SUPER_ADMIN);

    if (loginUserRoles.length > 0) {
      if (process.env.NEXT_NODE_ENV !== "staging" && !isSuperAdmin) {
        let deviceType = req.headers["platform-type"];
        if (!deviceType) {
          return res
            .status(StatusCodes.BAD_REQUEST)
            .send({
              status: false,
              message: res.__("ERROR_PLATEFORM_TYPE_REQUIRED"),
            });
        }

        deviceType = deviceType == 'ios' ? 'android' : deviceType
        const session = await UserSession.findOne({ where: { token, device_type: deviceType }, attributes: ['id'], raw: true });
        if (!session) {
          return res
            .status(StatusCodes.UNAUTHORIZED)
            .json({ status: false, message: res.__("FAIL_TOKEN_EXPIRED") });
        }
      }
    }


    if (getUserDetail) {
      if (getUserDetail.token_version && getUserDetail.token_version != 0) {
        if (getUserDetail.token_version != decoded.token_version) {
          return res
            .status(StatusCodes.UNAUTHORIZED)
            .send({
              status: false,
              message: res.__("USER_PASSOWORD_RESET_RE_LOGIN"),
            });
        }
      }
    }

    if (req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") {
      if (getUserDetail) {
        if (getUserDetail.pin_token_version && getUserDetail.pin_token_version != 0) {
          if (getUserDetail.pin_token_version != decoded.pin_token_version) {
            return res
              .status(StatusCodes.UNAUTHORIZED)
              .send({
                status: false,
                message: res.__("USER_PIN_RESET_RE_LOGIN"),
              });
          }
        }
      }
    }

    req.user = getUserDetail;

    next();
  } catch (e: any) {
    console.log(e);
    if (e.message == "jwt malformed") {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .send({ status: false, message: res.__("ERR_TOKEN_NOT_FOUND") });
    } else {
      if (e.name == "TokenExpiredError") {
        return res
          .status(StatusCodes.UNAUTHORIZED)
          .send({
            status: false,
            message: res.__("FAIL_TOKEN_EXPIRED"),
          });
      } else {
        return res.status(401).send({ status: false, message: e.message });
      }
    }
  }
};

export default userAuth;
