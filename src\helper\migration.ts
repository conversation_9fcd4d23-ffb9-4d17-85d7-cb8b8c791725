import { PutObjectCommand, GetObjectCommand, HeadBucketCommand, CreateBucketCommand } from "@aws-sdk/client-s3";
import { s3 } from "./upload.service";
import { getHash, ReadingFile } from "./common";
import { FILE_UPLOAD_CONSTANT } from "./constant";
import { Item, item_IEC, item_status, item_type, item_external_location, item_category } from "../models/Item";
import { User } from "../models/User";
import { Branch } from "../models/Branch";
import { UserEmploymentContract } from "../models/UserEmployementContract";
import { Setting } from "../models/Setting";
import { ChangeRequest } from "../models/ChangeRequest";
import { SideLetterConfirmation } from "../models/SideLetterConfirmation";
import { SideLetterItem } from "../models/SideLetterItem";
import { RightToWorkCheckList } from "../models/RightToWorkCheckList";
import { StarterForm } from "../models/StarterForm";
import { HrmcForm } from "../models/HrmcForm";
import { Media } from "../models/Media";
import { sequelize } from "../models";
import fs from "fs";
import path from "path";
import mmm from "mmmagic";
import { Op } from "sequelize";
import { DocumentCategory } from "../models/DocumentCategory";

interface FileToMigrate {
  localPath: string;
  s3Path: string;
  organizationId: string;
  userId?: string;
  fileType: string;
  category: string;
  model: any;
  recordId: number;
  fieldName: string;
  moveAfterUpload?: boolean;
  base64Data?: string;
  originalFileName?: string;
  isArrayField?: boolean;
}

interface UserWithOrg {
  organization_id: string;
}

interface UserEmploymentContractWithUser extends UserEmploymentContract {
  user_employment_contract?: UserWithOrg;
}

interface SideLetterConfirmationWithUser extends SideLetterConfirmation {
  sender_user?: UserWithOrg;
}

interface SideLetterItemWithConfirmation extends SideLetterItem {
  side_letter_confirmation?: SideLetterConfirmationWithUser;
}

const getFileType = (mimeType: string): string => {
  if (mimeType === "multipart/form-data") return item_type.VIDEO;
  if (mimeType === "application/octet-stream") return item_type.VIDEO;
  if (mimeType === "inode/x-empty") return item_type.IMAGE; // Handle empty files as images
  if (mimeType === "inode/x-stream") return item_type.IMAGE; // Handle stream files as images
  if (mimeType.split("/")[0] === "application") return "pdf";
  return mimeType.split("/")[0];
};

// Create bucket if it doesn't exist (matching API implementation)
const createBucketIfNotExists = async (bucketName: string) => {
  try {
    await s3.send(new HeadBucketCommand({ Bucket: bucketName }));
  } catch (error: any) {
    if (error.name === "NotFound" || error.name === "NoSuchBucket") {
      try {
        await s3.send(new CreateBucketCommand({ Bucket: bucketName }));
        console.log(`Bucket ${bucketName} created successfully`);
      } catch (createError: any) {
        console.error(`Error creating bucket: ${createError.message}`);
      }
    } else {
      console.error(`Error checking bucket: ${error.message}`);
    }
  }
};

// const getIECUnit = (size: number): item_IEC => {
//   if (size < 1024) return item_IEC.B;
//   if (size < 1024 * 1024) return item_IEC.KB;
//   if (size < 1024 * 1024 * 1024) return item_IEC.MB;
//   return item_IEC.GB;
// };

// Verification function to check migration completeness
export const verifyMigrationSections = () => {
  const sections = [
    "1. User Avatars & Signatures",
    "2. Branch Signatures",
    "3. Organization Settings (Brand Logo)",
    "4. Employment Contracts",
    "5. Side Letter Files",
    "6. Change Request Files",
    "7. StarterForm HMRC P45 Files",
    "8. Right To Work Checklist Files",
    "9. Media Files",
    "10. Document Category Images",
    "11. Document Category Items",
    "12. User Verification Files",
    "13. Notification Images"
  ];

  console.log("Migration script includes the following sections:");
  sections.forEach(section => console.log(`✓ ${section}`));
  console.log("\nAll sections have proper validation for:");
  console.log("✓ Numeric ID detection (already migrated files)");
  console.log("✓ Item table validation (item_location null check)");
  console.log("✓ File existence verification");
  console.log("✓ MIME type detection with fallbacks for JPG/inode issues");
  console.log("✓ Error handling and logging");
  console.log("✓ Onboarding documents validation");
  console.log("✓ HMRC P45 form migration support");

  return true;
};

export const migrateFilesToS3 = async () => {
  const transaction = await sequelize.transaction();
  const Magic = mmm.Magic;
  const magic = new Magic(mmm.MAGIC_MIME_TYPE);

  try {
    console.log("Starting file migration to S3...");
    const filesToMigrate: FileToMigrate[] = [];
    let totalFilesFound = 0;
    let totalFilesProcessed = 0;
    let totalFilesSkipped = 0;

    // 1. User Avatars
    const usersWithAvatars = await User.findAll({
      attributes: ["id", "user_avatar", "user_signature", "organization_id"],
      transaction
    });

    for (const user of usersWithAvatars) {
      if (user.user_avatar) {
        // Check if user_avatar is already an item ID (numeric) or a filename
        const isNumeric = /^\d+$/.test(String(user.user_avatar));

        if (!isNumeric) {
          // It's a filename, check if it needs migration
          const localPath = path.resolve(__dirname, "..", "uploads", "user_avatars", user.user_avatar);
          if (fs.existsSync(localPath)) {
            filesToMigrate.push({
              localPath,
              s3Path: FILE_UPLOAD_CONSTANT.USER_PROFILE_API.destinationPath(
                user.organization_id,
                user.id.toString(),
                user.user_avatar
              ),
              organizationId: user.organization_id,
              userId: user.id.toString(),
              fileType: "avatar",
              category: item_category.PROFILE,
              model: User,
              recordId: user.id,
              fieldName: "user_avatar"
            });
          }
        } else {
          // It's already an item ID, check if the item exists and has null item_location
          const existingItem = await Item.findByPk(parseInt(user.user_avatar), { transaction });
          if (existingItem && !existingItem.item_location && existingItem.item_name) {
            const localPath = path.resolve(__dirname, "..", "uploads", "user_avatars", existingItem.item_name);
            if (fs.existsSync(localPath)) {
              filesToMigrate.push({
                localPath,
                s3Path: FILE_UPLOAD_CONSTANT.USER_PROFILE_API.destinationPath(
                  user.organization_id,
                  user.id.toString(),
                  existingItem.item_name
                ),
                organizationId: user.organization_id,
                userId: user.id.toString(),
                fileType: "avatar",
                category: item_category.PROFILE,
                model: Item,
                recordId: existingItem.id,
                fieldName: "item_location"
              });
            }
          }
        }
      }
    }

    for (const user of usersWithAvatars) {
      if (user.user_signature) {
        // Check if user_signature is already an item ID (numeric) or a filename
        const isNumeric = /^\d+$/.test(String(user.user_signature));

        if (!isNumeric) {
          // It's a filename, check if it needs migration
          const localPath = path.resolve(__dirname, "..", "uploads", "signatures", user.user_signature);
          if (fs.existsSync(localPath)) {
            filesToMigrate.push({
              localPath,
              s3Path: FILE_UPLOAD_CONSTANT.USER_SIGNATURE_PATH.destinationPath(
                user.organization_id,
                user.id.toString(),
                user.user_signature
              ),
              organizationId: user.organization_id,
              userId: user.id.toString(),
              fileType: "signature",
              category: item_category.SIGNATURE,
              model: User,
              recordId: user.id,
              fieldName: "user_signature"
            });
          }
        } else {
          // It's already an item ID, check if the item exists and has null item_location
          const existingItem = await Item.findByPk(parseInt(user.user_signature), { transaction });
          if (existingItem && !existingItem.item_location && existingItem.item_name) {
            const localPath = path.resolve(__dirname, "..", "uploads", "signatures", existingItem.item_name);
            if (fs.existsSync(localPath)) {
              filesToMigrate.push({
                localPath,
                s3Path: FILE_UPLOAD_CONSTANT.USER_SIGNATURE_PATH.destinationPath(
                  user.organization_id,
                  user.id.toString(),
                  existingItem.item_name
                ),
                organizationId: user.organization_id,
                userId: user.id.toString(),
                fileType: "signature",
                category: item_category.SIGNATURE,
                model: Item,
                recordId: existingItem.id,
                fieldName: "item_location"
              });
            }
          }
        }
      }
    }

    // 2. Branch Signatures
    const branchesWithSignatures = await Branch.findAll({
      attributes: ["id", "branch_sign", "organization_id"],
      transaction
    });

    for (const branch of branchesWithSignatures) {
      if (branch.branch_sign) {
        // Check if branch_sign is already an item ID (numeric) or a filename
        const isNumeric = /^\d+$/.test(String(branch.branch_sign));

        if (!isNumeric) {
          // It's a filename, check if it needs migration
          const localPath = path.resolve(__dirname, "..", "uploads", "signatures", branch.branch_sign);
          if (fs.existsSync(localPath)) {
            filesToMigrate.push({
              localPath,
              s3Path: FILE_UPLOAD_CONSTANT.SIGNATURES_PATH.destinationPath(
                branch.organization_id,
                branch.branch_sign
              ),
              organizationId: branch.organization_id,
              fileType: "signature",
              category: item_category.SIGNATURE,
              model: Branch,
              recordId: branch.id,
              fieldName: "branch_sign"
            });
          }
        } else {
          // It's already an item ID, check if the item exists and has null item_location
          const existingItem = await Item.findByPk(parseInt(branch.branch_sign), { transaction });
          if (existingItem && !existingItem.item_location && existingItem.item_name) {
            const localPath = path.resolve(__dirname, "..", "uploads", "signatures", existingItem.item_name);
            if (fs.existsSync(localPath)) {
              filesToMigrate.push({
                localPath,
                s3Path: FILE_UPLOAD_CONSTANT.SIGNATURES_PATH.destinationPath(
                  branch.organization_id,
                  existingItem.item_name
                ),
                organizationId: branch.organization_id,
                fileType: "signature",
                category: item_category.SIGNATURE,
                model: Item,
                recordId: existingItem.id,
                fieldName: "item_location"
              });
            }
          }
        }
      }
    }

    // 3. Organization Settings (Brand Logo)
    const settingsWithLogo = await Setting.findAll({
      attributes: ["id", "value", "organization_id"],
      transaction
    });

    for (const setting of settingsWithLogo) {
      if (setting.value) {
        // Check if value is already an item ID (numeric) or a filename
        const isNumeric = /^\d+$/.test(String(setting.value));

        if (!isNumeric) {
          // It's a filename, check if it needs migration
          const localPath = path.resolve(__dirname, "..", "uploads", "settings", setting.value);
          if (fs.existsSync(localPath)) {
            filesToMigrate.push({
              localPath,
              s3Path: FILE_UPLOAD_CONSTANT.ORG_SETTINGS.destinationPath(
                setting.organization_id,
                setting.value
              ),
              organizationId: setting.organization_id,
              fileType: "logo",
              category: item_category.PROFILE,
              model: Setting,
              recordId: setting.id,
              fieldName: "value"
            });
          }
        } else {
          // It's already an item ID, check if the item exists and has null item_location
          const existingItem = await Item.findByPk(parseInt(setting.value), { transaction });
          if (existingItem && !existingItem.item_location && existingItem.item_name) {
            const localPath = path.resolve(__dirname, "..", "uploads", "settings", existingItem.item_name);
            if (fs.existsSync(localPath)) {
              filesToMigrate.push({
                localPath,
                s3Path: FILE_UPLOAD_CONSTANT.ORG_SETTINGS.destinationPath(
                  setting.organization_id,
                  existingItem.item_name
                ),
                organizationId: setting.organization_id,
                fileType: "logo",
                category: item_category.PROFILE,
                model: Item,
                recordId: existingItem.id,
                fieldName: "item_location"
              });
            }
          }
        }
      }
    }

    // 4. Employment Contracts
    const contracts = await UserEmploymentContract.findAll({
      include: [{
        model: User,
        as: "user_employment_contract",
        attributes: ["organization_id"]
      }],
      transaction
    }) as UserEmploymentContractWithUser[];

    for (const contract of contracts) {
      if (contract.contract_with_sign && contract.user_employment_contract?.organization_id) {
        // Check if contract_with_sign is already an item ID (numeric) or a filename
        const isNumeric = /^\d+$/.test(String(contract.contract_with_sign));

        if (!isNumeric) {
          // It's a filename, check if it needs migration
          const localPath = path.resolve(__dirname, "..", "uploads", "onbording_documents", contract.user_id.toString(), contract.contract_with_sign);
          if (fs.existsSync(localPath)) {
            filesToMigrate.push({
              localPath,
              s3Path: FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                contract.user_employment_contract.organization_id,
                contract.user_id.toString(),
                contract.contract_with_sign
              ),
              organizationId: contract.user_employment_contract.organization_id,
              userId: contract.user_id.toString(),
              fileType: "contract",
              category: item_category.DOCUMENT,
              model: UserEmploymentContract,
              recordId: contract.id,
              fieldName: "contract_with_sign"
            });
          }
        } else {
          // It's already an item ID, check if the item exists and has null item_location
          const existingItem = await Item.findByPk(parseInt(contract.contract_with_sign), { transaction });
          if (existingItem && !existingItem.item_location && existingItem.item_name) {
            const localPath = path.resolve(__dirname, "..", "uploads", "onbording_documents", contract.user_id.toString(), existingItem.item_name);
            if (fs.existsSync(localPath)) {
              filesToMigrate.push({
                localPath,
                s3Path: FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                  contract.user_employment_contract.organization_id,
                  contract.user_id.toString(),
                  existingItem.item_name
                ),
                organizationId: contract.user_employment_contract.organization_id,
                userId: contract.user_id.toString(),
                fileType: "contract",
                category: item_category.DOCUMENT,
                model: Item,
                recordId: existingItem.id,
                fieldName: "item_location"
              });
            }
          }
        }
      }
    }

    // 5. Side Letter Files
    const sideLetters = await SideLetterItem.findAll({
      include: [{
        model: SideLetterConfirmation,
        as: "side_letter_confirmation",
        include: [{
          model: User,
          as: "sender_user",
          attributes: ["organization_id"]
        }]
      }],
      transaction
    }) as SideLetterItemWithConfirmation[];

    for (const letterItem of sideLetters) {
      if (letterItem.side_letter_confirmation?.sender_user?.organization_id) {
        const item = await Item.findByPk(letterItem.item_id);
        if (item) {
          const localPath = path.resolve(__dirname, "..", "uploads", "side_letters", item.item_name);
          if (fs.existsSync(localPath)) {
            filesToMigrate.push({
              localPath,
              s3Path: FILE_UPLOAD_CONSTANT.SIDE_LETTER_FILES.destinationPath(
                letterItem.side_letter_confirmation.sender_user.organization_id,
                item.item_name
              ),
              organizationId: letterItem.side_letter_confirmation.sender_user.organization_id,
              fileType: "side_letter",
              category: item_category.DOCUMENT,
              model: Item,
              recordId: item.id,
              fieldName: "item_name"
            });
          }
        }
      }
    }

    // 6. Change Request Files
    const changeRequests = await ChangeRequest.findAll({
      include: [{
        model: User,
        as: "change_request_user",
        attributes: ["organization_id"]
      }],
      transaction
    }) as any[];

    for (const request of changeRequests) {
      if (request.change_request_user?.organization_id && request.change_request_files) {
        try {
          // Parse the JSON string to get file IDs/names
          let files: string[] = [];

          // Try to parse as JSON first (if it's already stored as JSON)
          try {
            files = JSON.parse(request.change_request_files);
          } catch {
            // If parsing fails, treat as single file
            files = [request.change_request_files];
          }

          // Ensure files is an array
          if (!Array.isArray(files)) {
            files = [files];
          }

          for (const file of files) {
            if (file && typeof file === 'string') {
              // Check if file is already an item ID (numeric string) or a filename
              const isNumeric = /^\d+$/.test(file);

              if (!isNumeric) {
                // It's a filename, try to migrate it
                const localPath = path.resolve(__dirname, "..", "uploads", "change_request", file);
                if (fs.existsSync(localPath)) {
                  filesToMigrate.push({
                    localPath,
                    s3Path: FILE_UPLOAD_CONSTANT.CHANGE_REQUEST_FILES.destinationPath(
                      request.change_request_user.organization_id,
                      request?.user_id.toString(),
                      file
                    ),
                    organizationId: request.change_request_user.organization_id,
                    fileType: "change_request",
                    category: item_category.DOCUMENT,
                    model: ChangeRequest,
                    recordId: request.id,
                    fieldName: "change_request_files",
                    originalFileName: file,
                    isArrayField: true
                  });
                }
              }
            }
          }
        } catch (error) {
          console.error(`Error processing change request files for request ${request.id}:`, error);
        }
      }
    }

    // 8. Right To Work Checklist Files
    const userChecklists: any = await RightToWorkCheckList.findAll({
      attributes: ['id', 'user_id', 'passport_front', 'passport_back', 'cv', 'share_code',
        'brp_front', 'brp_back', 'p45', 'ni_letter',
        'student_letter', 'photoID', 'statements_dl_utility',
      ],
      transaction
    });

    const checklistFields = [
      'passport_front', 'passport_back', 'cv', 'share_code',
      'brp_front', 'brp_back', 'p45', 'ni_letter',
      'student_letter', 'photoID', 'statements_dl_utility'
    ];

    for (let checklist of userChecklists) {
      checklist = JSON.parse(JSON.stringify(checklist));
      const getUserData = await User.findOne({
        attributes: ['organization_id'],
        where: { id: checklist.user_id },
        raw: true,
        transaction
      })
      if (getUserData?.organization_id) {
        for (const field of checklistFields) {
          const fileName = checklist[field];
          if (fileName && fileName !== '') {
            // Check if fileName is already an item ID (numeric) or a filename
            const isNumeric = /^\d+$/.test(String(fileName));

            if (!isNumeric) {
              // It's a filename, check if it needs migration
              const localPath = path.resolve(__dirname, '..', 'uploads', 'onbording_documents', String(checklist.user_id), `${fileName}`);
              if (fs.existsSync(localPath)) {
                filesToMigrate.push({
                  localPath,
                  s3Path: FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                    getUserData.organization_id,
                    String(checklist.user_id),
                    fileName
                  ),
                  organizationId: getUserData.organization_id,
                  userId: String(checklist.user_id),
                  fileType: "onboarding",
                  category: item_category.DOCUMENT,
                  model: RightToWorkCheckList,
                  recordId: checklist.id,
                  fieldName: field
                });
              }
            } else {
              // It's already an item ID, check if the item exists and has null item_location
              const existingItem = await Item.findByPk(parseInt(fileName), { transaction });
              if (existingItem && !existingItem.item_location && existingItem.item_name) {
                const localPath = path.resolve(__dirname, '..', 'uploads', 'onbording_documents', String(checklist.user_id), existingItem.item_name);
                if (fs.existsSync(localPath)) {
                  filesToMigrate.push({
                    localPath,
                    s3Path: FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                      getUserData.organization_id,
                      String(checklist.user_id),
                      existingItem.item_name
                    ),
                    organizationId: getUserData.organization_id,
                    userId: String(checklist.user_id),
                    fileType: "onboarding",
                    category: item_category.DOCUMENT,
                    model: Item,
                    recordId: existingItem.id,
                    fieldName: "item_location"
                  });
                }
              }
            }
          }
        }
      }
    }

    // 7. StarterForm HMRC P45 Files
    const starterForms = await StarterForm.findAll({
      attributes: ['id', 'user_id', 'hmrc_p45_form'],
      where: {
        hmrc_p45_form: {
          [Op.ne]: null
        }
      } as any,
      transaction
    });

    for (const starterForm of starterForms) {
      if (starterForm.hmrc_p45_form) {
        const getUserData = await User.findOne({
          attributes: ['organization_id'],
          where: { id: starterForm.user_id },
          raw: true,
          transaction
        });

        if (getUserData?.organization_id) {
          // Check if hmrc_p45_form is already an item ID (numeric) or a filename
          const isNumeric = /^\d+$/.test(String(starterForm.hmrc_p45_form));

          if (!isNumeric) {
            // It's a filename, check if it needs migration
            const localPath = path.resolve(__dirname, '..', 'uploads', 'onbording_documents', String(starterForm.user_id), starterForm.hmrc_p45_form);
            if (fs.existsSync(localPath)) {
              filesToMigrate.push({
                localPath,
                s3Path: FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                  getUserData.organization_id,
                  String(starterForm.user_id),
                  starterForm.hmrc_p45_form
                ),
                organizationId: getUserData.organization_id,
                userId: String(starterForm.user_id),
                fileType: "hmrc_p45",
                category: item_category.DOCUMENT,
                model: StarterForm,
                recordId: starterForm.id,
                fieldName: "hmrc_p45_form"
              });
            }
          } else {
            // It's already an item ID, check if the item exists and has null item_location
            const existingItem = await Item.findByPk(parseInt(starterForm.hmrc_p45_form), { transaction });
            if (existingItem && !existingItem.item_location && existingItem.item_name) {
              const localPath = path.resolve(__dirname, '..', 'uploads', 'onbording_documents', String(starterForm.user_id), existingItem.item_name);
              if (fs.existsSync(localPath)) {
                filesToMigrate.push({
                  localPath,
                  s3Path: FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                    getUserData.organization_id,
                    String(starterForm.user_id),
                    existingItem.item_name
                  ),
                  organizationId: getUserData.organization_id,
                  userId: String(starterForm.user_id),
                  fileType: "hmrc_p45",
                  category: item_category.DOCUMENT,
                  model: Item,
                  recordId: existingItem.id,
                  fieldName: "item_location"
                });
              }
            }
          }
        }
      }
    }

    /** User Verification Images */
    // 12. User Verification Files
    const usersWithVerificationFiles = await User.findAll({
      attributes: ["id", "user_verification_doc", "organization_id"],
      where: {
        user_verification_doc: {
          [Op.ne]: null
        }
      } as any,
      transaction
    });

    for (const user of usersWithVerificationFiles) {
      if (user.user_verification_doc && user.organization_id) {
        const existingItem = await Item.findOne({ where: { id: user.user_verification_doc }, transaction });
        if (existingItem && !existingItem.item_location && existingItem.item_name) {
          const localPath = path.resolve(__dirname, "..", "uploads", "user_verification", existingItem.item_name);
          if (fs.existsSync(localPath)) {
            filesToMigrate.push({
              localPath,
              s3Path: FILE_UPLOAD_CONSTANT.USER_VERIFICATION.destinationPath(
                user.organization_id,
                existingItem.item_name,
                user.id.toString()
              ),
              organizationId: user.organization_id,
              fileType: "user_verification",
              category: item_category.PROFILE,
              model: Item,
              recordId: existingItem.id,
              fieldName: "item_location",
              isArrayField: false
            });
          }
        } else {
          const localPath = path.resolve(__dirname, "..", "uploads", "user_verification", user.user_verification_doc);
          if (fs.existsSync(localPath)) {
            filesToMigrate.push({
              localPath,
              s3Path: FILE_UPLOAD_CONSTANT.USER_VERIFICATION.destinationPath(
                user.organization_id,
                user.user_verification_doc,
                user.id.toString()
              ),
              organizationId: user.organization_id,
              fileType: "user_verification",
              category: item_category.PROFILE,
              model: User,
              recordId: user.id,
              fieldName: "user_verification_doc",
              isArrayField: false
            });
          }
        }
      }
    }

    // 9. Media Files
    const mediaFiles = await sequelize.query(`
      SELECT m.id, m.media_name, m.is_external_link, u.organization_id
      FROM nv_media m
      JOIN nv_users u ON u.id = m.created_by
      WHERE m.is_external_link = false
        AND m.media_name IS NOT NULL
        AND m.media_name != ''
        AND m.media_name NOT REGEXP '^[0-9]+$'
    `, {
      type: sequelize.QueryTypes.SELECT,
      transaction
    }) as Array<{ id: number; media_name: string; is_external_link: boolean; organization_id: string }>;

    for (const media of mediaFiles) {
      if (media.media_name && !media.is_external_link && media.organization_id) {
        const localPath = path.resolve(__dirname, "..", "uploads", "media", media.media_name);
        if (fs.existsSync(localPath)) {
          filesToMigrate.push({
            localPath,
            s3Path: FILE_UPLOAD_CONSTANT.MEDIA_FILES.destinationPath(
              media.organization_id,
              media.media_name
            ),
            organizationId: media.organization_id,
            fileType: "media",
            category: item_category.OTHER,
            model: Media,
            recordId: media.id,
            fieldName: "media_name"
          });
        }
      }
    }

    // 10. Document Category Images
    const documentCategories = await DocumentCategory.findAll({
      attributes: ["id", "category_image", "organization_id"],
      where: {
        category_image: {
          [Op.ne]: null
        }
      } as any,
      transaction
    });

    for (const category of documentCategories) {
      if (category.category_image && category.organization_id) {
        // Document category images are already stored as item IDs, check if item needs migration
        const existingItem = await Item.findOne({ where: { id: category.category_image }, transaction })
        if(existingItem && !existingItem.item_location && existingItem.item_name){
          const localPath = path.resolve(__dirname, "..", "uploads", "document_category", String(existingItem.item_name));
          if (fs.existsSync(localPath)) {
            filesToMigrate.push({
              localPath,
              s3Path: FILE_UPLOAD_CONSTANT.DOCUMENT_CATEGORY_FILES.destinationPath(
                category.organization_id,
                String(existingItem.item_name)
              ),
              organizationId: category.organization_id,
              fileType: "category_image",
              category: item_category.CATEGORY_ICON,
              model: Item,
              recordId: existingItem.id,
              fieldName: "item_location"
            });
          }
        }
      }
    }

    // 11. Document Category Items (item_list files)
    const documentCategoryItems = await sequelize.query(`
      SELECT dci.category_id, dci.item_id, dc.organization_id
      FROM nv_document_category_item dci
      JOIN nv_document_category dc ON dc.id = dci.category_id
      WHERE dci.item_id IS NOT NULL
    `, {
      type: sequelize.QueryTypes.SELECT,
      transaction
    }) as Array<{ category_id: number; item_id: number; organization_id: string }>;

    for (const categoryItem of documentCategoryItems) {
      if (categoryItem.item_id && categoryItem.organization_id) {
        // Check if this item_id points to a file that needs migration
        const existingItem = await Item.findByPk(categoryItem.item_id, { transaction });
        if (existingItem && !existingItem.item_location && existingItem.item_name) {
          const localPath = path.resolve(__dirname, "..", "uploads", "document_category", existingItem.item_name);
          if (fs.existsSync(localPath)) {
            filesToMigrate.push({
              localPath,
              s3Path: FILE_UPLOAD_CONSTANT.DOCUMENT_CATEGORY_FILES.destinationPath(
                categoryItem.organization_id,
                existingItem.item_name
              ),
              organizationId: categoryItem.organization_id,
              fileType: "category_item",
              category: item_category.DOCUMENT,
              model: Item,
              recordId: existingItem.id,
              fieldName: "item_location"
            });
          }
        }
      }
    }

    // 12. Notification Images (from notification_meta table)
    const notificationImages = await sequelize.query(`
      SELECT DISTINCT nm.notification_image, u.organization_id
      FROM notification_meta nm
      JOIN nv_users u ON u.id = nm.from_user_id
      WHERE nm.notification_image IS NOT NULL
        AND nm.notification_image != ''
        AND nm.notification_image NOT REGEXP '^[0-9]+$'
    `, {
      type: sequelize.QueryTypes.SELECT,
      transaction
    }) as Array<{ notification_image: string; organization_id: string }>;

    for (const notification of notificationImages) {
      if (notification.notification_image && notification.organization_id) {
        const localPath = path.resolve(__dirname, "..", "uploads", "notification", notification.notification_image);
        if (fs.existsSync(localPath)) {
          filesToMigrate.push({
            localPath,
            s3Path: FILE_UPLOAD_CONSTANT.NOTIFICATION_FILES.destinationPath(
              notification.organization_id,
              notification.notification_image
            ),
            organizationId: notification.organization_id,
            fileType: "notification",
            category: item_category.OTHER,
            model: null, // Will be handled via raw query update
            recordId: 0,
            fieldName: "notification_image",
            originalFileName: notification.notification_image
          });
        }
      }
    }

    // Create an array to track file updates for array fields
    interface ArrayFieldUpdate {
      modelName: string;
      recordId: number;
      fieldName: string;
      fileMap: Map<string, string>;
    }
    const arrayFieldUpdates: ArrayFieldUpdate[] = [];

    // Ensure bucket exists
    const bucketName = process.env.NODE_ENV || "development";
    await createBucketIfNotExists(bucketName);

    totalFilesFound = filesToMigrate.length;
    console.log(`Found ${totalFilesFound} files to migrate`);

    // Process each file
    for (const file of filesToMigrate) {
      try {
        console.log(`Processing file: ${file.localPath} (Type: ${file.fileType}, Category: ${file.category}, Model: ${file.model?.name || 'N/A'}, Field: ${file.fieldName})`);

        // Check if file still exists before processing
        if (!fs.existsSync(file.localPath)) {
          console.warn(`File no longer exists, skipping: ${file.localPath}`);
          totalFilesSkipped++;
          continue;
        }

        // Read file using the same method as API
        const originalFile: any = await ReadingFile(file.localPath);
        if (!originalFile.status) {
          console.error(`Failed to read file: ${file.localPath}`, originalFile.data);
          totalFilesSkipped++;
          continue;
        }

        const fileBuffer = fs.readFileSync(file.localPath);

        // Get MIME type using mmmagic with fallback logic
        let mimeType = await new Promise<string>((resolve, reject) => {
          magic.detectFile(file.localPath, (err: Error | null, result: string) => {
            if (err) reject(err);
            else resolve(result);
          });
        });

        // Handle problematic MIME types and provide fallbacks
        if (mimeType === "inode/x-empty" || mimeType === "inode/x-stream" || !mimeType) {
          // Fallback to file extension-based detection
          const ext = path.extname(file.localPath).toLowerCase();
          switch (ext) {
            case '.jpg':
            case '.jpeg':
              mimeType = 'image/jpeg';
              break;
            case '.png':
              mimeType = 'image/png';
              break;
            case '.gif':
              mimeType = 'image/gif';
              break;
            case '.pdf':
              mimeType = 'application/pdf';
              break;
            case '.doc':
              mimeType = 'application/msword';
              break;
            case '.docx':
              mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
              break;
            case '.mp4':
              mimeType = 'video/mp4';
              break;
            case '.mp3':
              mimeType = 'audio/mpeg';
              break;
            default:
              mimeType = 'application/octet-stream';
          }
          console.log(`Fallback MIME type detection for ${file.localPath}: ${mimeType}`);
        }

        // Generate hash for deduplication using the same method as API
        const fileHash = await getHash(originalFile.data, {
          path: file.localPath,
          originalname: path.basename(file.localPath),
          size: fileBuffer.length,
          mimetype: mimeType
        }) as { status: boolean; hash: string };

        if (!fileHash.status) {
          console.error(`Failed to generate hash for file: ${file.localPath}`);
          totalFilesSkipped++;
          continue;
        }
        // Check if file already exists by hash (matching API logic)
        let fileExists = false;
        let fileExistAtLocation = false;
        let existingItem: any = null;

        try {
          if (fileHash.status) {
            existingItem = await Item.findOne({
              where: {
                item_hash: fileHash.hash,
                item_organization_id: file.organizationId
              },
              transaction
            });

            if (existingItem && existingItem.id) {
              fileExists = true;
              // Check if file exists in S3 (matching API implementation)
              try {
                const checkS3FileExist = await s3.send(
                  new GetObjectCommand({
                    Bucket: bucketName,
                    Key: existingItem.item_location,
                  })
                );
                if (checkS3FileExist && checkS3FileExist.Body) {
                  fileExistAtLocation = true;
                }
              } catch (s3Error) {
                // File doesn't exist in S3, will need to upload
                fileExistAtLocation = false;
              }
            }
          }
        } catch (error) {
          // File doesn't exist, continue with upload
          fileExists = false;
        }

        let itemId;

        if (!fileExists) {
          // Upload to S3
          await s3.send(
            new PutObjectCommand({
              Bucket: bucketName,
              Key: file.s3Path,
              Body: fileBuffer,
              ContentType: mimeType
            })
          );

          // Create Item record using API logic
          const saveItem: any = {
            item_type: getFileType(mimeType),
            item_name: path.basename(file.localPath),
            item_hash: fileHash.hash,
            item_mime_type: mimeType,
            item_extension: path.extname(file.localPath),
            item_size: fileBuffer.length,
            item_IEC: item_IEC.B,
            item_status: item_status.ACTIVE,
            item_external_location: item_external_location.NO,
            item_location: file.s3Path,
            item_organization_id: file.organizationId,
            item_category: file.category
          };

          const newItem = await Item.create(saveItem, { transaction });
          itemId = newItem.id;
        } else {
          // File exists by hash, but check if it exists in S3
          if (!fileExistAtLocation) {
            // Upload file to S3 and update location
            await s3.send(
              new PutObjectCommand({
                Bucket: bucketName,
                Key: file.s3Path,
                Body: fileBuffer,
                ContentType: mimeType,
              })
            );

            // Update item location
            await Item.update(
              {
                item_location: file.s3Path,
                item_status: item_status.ACTIVE,
              },
              {
                where: { id: existingItem.id },
                transaction
              }
            );
          }
          itemId = existingItem.id;
        }

        // Handle array fields (like change_request_files)
        if (file.isArrayField) {
          // Find existing update entry or create new one
          let updateEntry = arrayFieldUpdates.find(entry =>
            entry.modelName === file.model.name &&
            entry.recordId === file.recordId &&
            entry.fieldName === file.fieldName
          );

          if (!updateEntry) {
            updateEntry = {
              modelName: file.model.name,
              recordId: file.recordId,
              fieldName: file.fieldName,
              fileMap: new Map<string, string>()
            };
            arrayFieldUpdates.push(updateEntry);
          }

          updateEntry.fileMap.set(file.originalFileName || path.basename(file.localPath), String(itemId));
        } else {
          console.log("Updating table", file.model.name, " is item:", fileExists, " in s3?:", fileExistAtLocation, " field name?:", file.fieldName)
          // Handle single file fields
          if (file.model.name == "Item" && file.fieldName == "item_location") {
            await file.model.update(
              { [file.fieldName]: file.s3Path },
              { where: { id: file.recordId }, transaction }
            );
          } else {
            await file.model.update(
              { [file.fieldName]: String(itemId) },
              { where: { id: file.recordId }, transaction }
            );
          }
        }

        console.log(`Successfully processed file: ${file.localPath} -> Item ID: ${itemId} (MIME: ${mimeType})`);
        totalFilesProcessed++;

        // Delete local file after successful migration (uncomment when ready)
        // fs.unlinkSync(file.localPath);
      } catch (fileError) {
        console.error(`Error processing file ${file.localPath}:`, fileError);
        console.error(`File details: Type=${file.fileType}, Category=${file.category}, Model=${file.model?.name || 'N/A'}, Field=${file.fieldName}`);
        totalFilesSkipped++;
        // Continue with next file instead of failing entire migration
      }
    }

    // Update array fields
    for (const updateEntry of arrayFieldUpdates) {
      const { modelName, recordId, fieldName, fileMap } = updateEntry;

      try {
        if (modelName === 'ChangeRequest' && fieldName === 'change_request_files') {
          // Get the current record
          const record = await ChangeRequest.findByPk(recordId, { transaction });
          if (record && record.change_request_files) {
            let files: string[] = [];
            try {
              files = JSON.parse(record.change_request_files);
            } catch {
              files = [record.change_request_files];
            }

            if (!Array.isArray(files)) {
              files = [files];
            }

            // Replace filenames with item IDs
            const updatedFiles = files.map((file: string) => {
              return fileMap.has(file) ? fileMap.get(file)! : file;
            });

            await ChangeRequest.update(
              { change_request_files: JSON.stringify(updatedFiles) },
              { where: { id: recordId }, transaction }
            );

            console.log(`Updated ${modelName} record ${recordId} field ${fieldName} with ${updatedFiles.length} files`);
          }
        }
        // Add more model types here if needed in the future
        // else if (modelName === 'OtherModel' && fieldName === 'other_files_field') {
        //   // Handle other array field types
        // }
      } catch (error) {
        console.error(`Error updating ${modelName} record ${recordId} field ${fieldName}:`, error);
      }
    }

    // Handle notification image updates (special case for raw query table)
    const notificationImageUpdates = filesToMigrate.filter(file =>
      file.fieldName === "notification_image" && file.originalFileName
    );

    for (const notificationFile of notificationImageUpdates) {
      try {
        // Find the corresponding item ID for this file
        const processedFile = filesToMigrate.find(f =>
          f.localPath === notificationFile.localPath
        );

        if (processedFile) {
          // Get the item ID that was created/found for this file
          const item = await Item.findOne({
            where: {
              item_hash: {
                [Op.ne]: null
              },
              item_organization_id: notificationFile.organizationId,
              item_location: notificationFile.s3Path
            } as any,
            transaction
          });

          if (item) {
            // Update notification_meta table
            await sequelize.query(`
              UPDATE notification_meta
              SET notification_image = :itemId
              WHERE notification_image = :originalFileName
            `, {
              replacements: {
                itemId: item.id,
                originalFileName: notificationFile.originalFileName
              },
              type: sequelize.QueryTypes.UPDATE,
              transaction
            });

            console.log(`Updated notification_meta records: ${notificationFile.originalFileName} -> Item ID: ${item.id}`);
          }
        }
      } catch (error) {
        console.error(`Error updating notification image for file ${notificationFile.originalFileName}:`, error);
      }
    }

    await transaction.commit();
    console.log("Migration completed successfully");
    console.log(`Migration Statistics:`);
    console.log(`- Total files found: ${totalFilesFound}`);
    console.log(`- Files processed: ${totalFilesProcessed}`);
    console.log(`- Files skipped: ${totalFilesSkipped}`);
    console.log(`- Array field updates: ${arrayFieldUpdates.length}`);

    // Verification summary
    console.log(`\nMigration Summary by Type:`);
    const typeStats = filesToMigrate.reduce((acc, file) => {
      acc[file.fileType] = (acc[file.fileType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    Object.entries(typeStats).forEach(([type, count]) => {
      console.log(`- ${type}: ${count} files`);
    });

  } catch (error) {
    await transaction.rollback();
    console.error("Migration failed:", error);
    throw error;
  }
};
