import {
  UserEmploymentContract,
  contract_status,
} from "../models/UserEmployementContract";
import { UserRequest, request_status } from "../models/UserRequest";
import { Branch } from "../models/Branch";
import { Department } from "../models/Department";
import moment from "moment";

// Configure moment.js for consistent behavior
moment.locale("en"); // Set default locale
import { User, user_status } from "../models/User";
import { ChangeRequest } from "../models/ChangeRequest";
import { Resignation } from "../models/Resignation";
import { sequelize } from "../models";
import { Op, QueryTypes } from "sequelize";

/**
 * Dashboard Service
 * Contains data functions for retrieving dashboard widgets
 */

// Enums for chart identification
enum ChartType {
  ONBOARDING_LINE = "onboarding_line",
  ONBOARDING_BAR = "onboarding_bar",
  CONTRACT_PIE = "contract_pie",
  LEAVE_COMPARISON = "leave_comparison",
}

// Types for Recharts compatibility
interface RechartsDataPoint {
  name: string;
  value: number;
  [key: string]: any; // Allow additional properties for multi-series data
}

interface RechartsLineBarData {
  data: RechartsDataPoint[];
  xAxisKey: string;
  yAxisKey: string;
  series: {
    dataKey: string;
    name: string;
    color: string;
    type?: "line" | "bar";
  }[];
}

interface RechartsPieData {
  data: RechartsDataPoint[];
  nameKey: string;
  valueKey: string;
  colors: string[];
}

interface CommonChartResponse {
  success: boolean;
  chartType: "line" | "bar" | "pie";
  title: string;
  data: RechartsLineBarData | RechartsPieData;
  metadata?: {
    totalRecords: number;
    dateRange?: string;
    filters?: any;
    lastUpdated: string;
  };
}

// Chart configuration interface
interface ChartConfig {
  type: ChartType;
  widgetType: string;
  title: string;
  chartType: "line" | "bar" | "pie";
  category: "chart";
  description: string;
  hasFilters: boolean;
  dataFetcher: (
    organizationId: string,
    filters?: FilterOptions
  ) => Promise<CommonChartResponse>;
}

interface FilterOptions {
  branchId?: number;
  widgetType?: "counts" | "charts" | "all";
  chartType?: "line" | "bar" | "pie";
}

// Cache for frequently accessed data (in production, use Redis or similar)
const dataCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get cached data or fetch if expired
 */
const getCachedData = async <T>(
  key: string,
  fetchFn: () => Promise<T>,
  ttl: number = CACHE_TTL
): Promise<T> => {
  const cached = dataCache.get(key);
  const now = Date.now();

  if (cached && now - cached.timestamp < ttl) {
    return cached.data as T;
  }

  const data = await fetchFn();
  dataCache.set(key, { data, timestamp: now });
  return data;
};

/**
 * Validate required parameters
 */
const validateParameters = (organizationId: string, userId: number): void => {
  if (!organizationId || typeof organizationId !== "string") {
    throw new Error("Invalid organizationId: must be a non-empty string");
  }
  if (!userId || typeof userId !== "number" || userId <= 0) {
    throw new Error("Invalid userId: must be a positive number");
  }
};

/**
 * Generate date range for the last N months using Moment.js
 */
const generateMonthRange = (
  months: number
): { start: Date; monthKeys: string[]; monthLabels: string[] } => {
  const start = moment().subtract(months, "months").startOf("month").toDate();

  const monthKeys: string[] = [];
  const monthLabels: string[] = [];

  for (let i = months - 1; i >= 0; i--) {
    const monthMoment = moment().subtract(i, "months");
    monthKeys.push(monthMoment.format("YYYY-MM"));
    monthLabels.push(monthMoment.format("MMM YYYY"));
  }

  return { start, monthKeys, monthLabels };
};

/**
 * Utility functions for consistent date handling with Moment.js
 */
const DateUtils = {
  /**
   * Validate and format date to YYYY-MM format for month grouping
   */
  toMonthKey: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to toMonthKey: ${date}`);
      return moment().format("YYYY-MM"); // Return current month as fallback
    }
    return momentDate.format("YYYY-MM");
  },

  /**
   * Format date to human-readable month label
   */
  toMonthLabel: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to toMonthLabel: ${date}`);
      return moment().format("MMM YYYY"); // Return current month as fallback
    }
    return momentDate.format("MMM YYYY");
  },

  /**
   * Check if date is within the last N months
   */
  isWithinLastMonths: (
    date: string | Date | moment.Moment,
    months: number
  ): boolean => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to isWithinLastMonths: ${date}`);
      return false;
    }
    const cutoffDate = moment().subtract(months, "months").startOf("month");
    return momentDate.isAfter(cutoffDate);
  },

  /**
   * Get start of month for a given date
   */
  startOfMonth: (date: string | Date | moment.Moment): Date => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to startOfMonth: ${date}`);
      return moment().startOf("month").toDate(); // Return current month start as fallback
    }
    return momentDate.startOf("month").toDate();
  },

  /**
   * Format date for display with validation
   */
  formatForDisplay: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to formatForDisplay: ${date}`);
      return moment().format("YYYY-MM-DD"); // Return current date as fallback
    }
    return momentDate.format("YYYY-MM-DD");
  },

  /**
   * Get relative time from now (e.g., "2 days ago")
   */
  fromNow: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to fromNow: ${date}`);
      return "Invalid date";
    }
    return momentDate.fromNow();
  },

  /**
   * Check if date is valid
   */
  isValid: (date: string | Date | moment.Moment): boolean => {
    return moment(date).isValid();
  },

  /**
   * Get current timestamp in ISO format
   */
  now: (): string => {
    return moment().toISOString();
  },
};

/**
 * Get onboarding pipeline status data for line chart (Recharts format)
 * X-axis: Branches, Y-axis: User counts
 * 2 data series: Completed, Verified
 */
const getOnboardingPipelineLineData = async (
  organizationId: string,
  filters?: FilterOptions
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `onboarding_line_${organizationId}_${filters?.branchId || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build where clauses for branches and users
      const branchWhere: any = {
        organization_id: organizationId,
        branch_status: "active",
      };

      const userWhere: any = {
        organization_id: organizationId,
      };

      // Apply branch filtering if specified
      if (filters?.branchId) {
        branchWhere.id = filters.branchId;
        userWhere.branch_id = filters.branchId;
      }

      // Execute queries in parallel for better performance
      const [branches, users] = await Promise.all([
        Branch.findAll({
          where: branchWhere,
          attributes: ["id", "branch_name"],
          raw: true,
        }),
        User.findAll({
          where: userWhere,
          attributes: ["id", "branch_id", "user_status"],
          raw: true,
        }),
      ]);

      if (branches.length === 0) {
        return {
          success: true,
          chartType: "line",
          title: "Onboarding Pipeline Status by Branch",
          data: {
            data: [],
            xAxisKey: "name",
            yAxisKey: "value",
            series: [
              {
                dataKey: "completed",
                name: "Completed",
                color: "#10B981",
                type: "line",
              },
              {
                dataKey: "verified",
                name: "Verified",
                color: "#8B5CF6",
                type: "line",
              },
            ],
          } as RechartsLineBarData,
          metadata: {
            totalRecords: 0,
            lastUpdated: DateUtils.now(),
          },
        };
      }

      // Initialize branch data structure
      const branchData = new Map<
        number,
        {
          name: string;
          completed: number;
          verified: number;
        }
      >();

      // Initialize all branches with zero counts
      branches.forEach((branch: any) => {
        branchData.set(branch.id, {
          name: branch.branch_name,
          completed: 0,
          verified: 0,
        });
      });

      // Aggregate user counts by branch and status
      users.forEach((user: any) => {
        const branchInfo = branchData.get(user.branch_id);
        if (branchInfo) {
          switch (user.user_status) {
            case user_status.COMPLETED:
              branchInfo.completed++;
              break;
            case user_status.VERIFIED:
              branchInfo.verified++;
              break;
            // Skip pending, active, and ongoing statuses
          }
        }
      });

      // Transform to Recharts format
      const chartData: RechartsDataPoint[] = Array.from(
        branchData.values()
      ).map((branch) => ({
        name: branch.name,
        value: branch.completed + branch.verified, // Total for reference
        completed: branch.completed,
        verified: branch.verified,
      }));

      const totalRecords = users.length;

      return {
        success: true,
        chartType: "line",
        title: "Onboarding Pipeline Status by Branch",
        data: {
          data: chartData,
          xAxisKey: "name",
          yAxisKey: "value",
          series: [
            {
              dataKey: "completed",
              name: "Completed",
              color: "#10B981",
              type: "line",
            },
            {
              dataKey: "verified",
              name: "Verified",
              color: "#8B5CF6",
              type: "line",
            },
          ],
        } as RechartsLineBarData,
        metadata: {
          totalRecords,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getOnboardingPipelineLineData:", error);
      // Return empty structure instead of throwing to prevent cascade failures
      return {
        success: false,
        chartType: "line",
        title: "Onboarding Pipeline Status by Branch",
        data: {
          data: [],
          xAxisKey: "name",
          yAxisKey: "value",
          series: [
            {
              dataKey: "completed",
              name: "Completed",
              color: "#10B981",
              type: "line",
            },
            {
              dataKey: "verified",
              name: "Verified",
              color: "#8B5CF6",
              type: "line",
            },
          ],
        } as RechartsLineBarData,
        metadata: {
          totalRecords: 0,
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

/**
 * Get onboarding pipeline status data for bar chart (Recharts format)
 * X-axis: Branches, Y-axis: User counts
 * 3 data series: Pending, Completed, Verified
 */
const getOnboardingPipelineBarData = async (
  organizationId: string
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `onboarding_bar_${organizationId}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Execute queries in parallel for better performance
      const [branches, users] = await Promise.all([
        Branch.findAll({
          where: {
            organization_id: organizationId,
            branch_status: "active",
          },
          attributes: ["id", "branch_name"],
          raw: true,
        }),
        User.findAll({
          where: {
            organization_id: organizationId,
          },
          attributes: ["id", "branch_id", "user_status"],
          raw: true,
        }),
      ]);

      if (branches.length === 0) {
        return {
          success: true,
          chartType: "bar",
          title: "Onboarding Pipeline Status by Branch",
          data: {
            data: [],
            xAxisKey: "name",
            yAxisKey: "value",
            series: [],
          } as RechartsLineBarData,
          metadata: {
            totalRecords: 0,
            lastUpdated: DateUtils.now(),
          },
        };
      }

      // Initialize branch data structure
      const branchData = new Map<
        number,
        {
          name: string;
          pending: number;
          completed: number;
          verified: number;
        }
      >();

      // Initialize all branches with zero counts
      branches.forEach((branch: any) => {
        branchData.set(branch.id, {
          name: branch.branch_name,
          pending: 0,
          completed: 0,
          verified: 0,
        });
      });

      // Aggregate user counts by branch and status
      users.forEach((user: any) => {
        const branchInfo = branchData.get(user.branch_id);
        if (branchInfo) {
          switch (user.user_status) {
            case user_status.PENDING:
            case user_status.ACTIVE:
            case user_status.ONGOING:
              branchInfo.pending++;
              break;
            case user_status.COMPLETED:
              branchInfo.completed++;
              break;
            case user_status.VERIFIED:
              branchInfo.verified++;
              break;
          }
        }
      });

      // Transform to Recharts format
      const chartData: RechartsDataPoint[] = Array.from(
        branchData.values()
      ).map((branch) => ({
        name: branch.name,
        value: branch.pending + branch.completed + branch.verified, // Total for reference
        pending: branch.pending,
        completed: branch.completed,
        verified: branch.verified,
      }));

      const totalRecords = users.length;

      return {
        success: true,
        chartType: "bar",
        title: "Onboarding Pipeline Status by Branch",
        data: {
          data: chartData,
          xAxisKey: "name",
          yAxisKey: "value",
          series: [
            {
              dataKey: "pending",
              name: "Pending",
              color: "#F59E0B",
              type: "bar",
            },
            {
              dataKey: "completed",
              name: "Completed",
              color: "#10B981",
              type: "bar",
            },
            {
              dataKey: "verified",
              name: "Verified",
              color: "#8B5CF6",
              type: "bar",
            },
          ],
        } as RechartsLineBarData,
        metadata: {
          totalRecords,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getOnboardingPipelineBarData:", error);
      return {
        success: false,
        chartType: "bar",
        title: "Onboarding Pipeline Status by Branch",
        data: {
          data: [],
          xAxisKey: "name",
          yAxisKey: "value",
          series: [],
        } as RechartsLineBarData,
        metadata: {
          totalRecords: 0,
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

/**
 * Get user contract status data for pie chart (Recharts format)
 * Optimized with caching and better query structure
 */
const getUserContractPieData = async (
  organizationId: string,
  filters?: FilterOptions
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `contract_pie_${organizationId}_${filters?.branchId || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build where clause for User table
      const userWhere: any = {
        organization_id: organizationId,
      };

      if (filters?.branchId) {
        userWhere.branch_id = filters.branchId;
      }

      const contractCounts = await UserEmploymentContract.findAll({
        include: [
          {
            model: User,
            as: "user_employment_contract",
            where: userWhere,
            attributes: [],
          },
        ],
        attributes: [
          "contract_status",
          [
            UserEmploymentContract.sequelize!.fn(
              "COUNT",
              UserEmploymentContract.sequelize!.col("UserEmploymentContract.id")
            ),
            "count",
          ],
        ],
        group: ["contract_status"],
        raw: true,
      });

      const statusMap = {
        [contract_status.ACTIVE]: "Active",
        [contract_status.INACTIVE]: "Inactive",
        [contract_status.DELETED]: "Terminated",
      };

      const statusColors = ["#10B981", "#F59E0B", "#EF4444"];

      // Format data for Recharts pie chart
      const chartData: RechartsDataPoint[] = contractCounts
        .filter(
          (item: any) =>
            statusMap[item.contract_status as keyof typeof statusMap]
        )
        .map((item: any, index: number) => {
          const statusName =
            statusMap[item.contract_status as keyof typeof statusMap];
          return {
            name: statusName,
            value: parseInt(item.count, 10) || 0,
            status: statusName,
            count: parseInt(item.count, 10) || 0,
            color: statusColors[index % statusColors.length],
          };
        });

      const totalRecords = chartData.reduce((sum, item) => sum + item.value, 0);

      return {
        success: true,
        chartType: "pie",
        title: "User Contract Distribution",
        data: {
          data: chartData,
          nameKey: "name",
          valueKey: "value",
          colors: statusColors,
        } as RechartsPieData,
        metadata: {
          totalRecords,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getUserContractPieData:", error);
      return {
        success: false,
        chartType: "pie",
        title: "User Contract Distribution",
        data: {
          data: [],
          nameKey: "name",
          valueKey: "value",
          colors: [],
        } as RechartsPieData,
        metadata: {
          totalRecords: 0,
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

/**
 * Get leave comparison data by branch for line chart (Recharts format)
 * Heavily optimized with parallel queries and efficient data processing
 */
const getLeaveComparisonByBranchData = async (
  organizationId: string,
  filters?: FilterOptions
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `leave_comparison_${organizationId}_${filters?.branchId || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      const { monthKeys, monthLabels } = generateMonthRange(6);

      // Build where clauses
      const branchWhere: any = {
        organization_id: organizationId,
      };

      const userWhere: any = {
        organization_id: organizationId,
      };

      if (filters?.branchId) {
        branchWhere.id = filters.branchId;
        userWhere.branch_id = filters.branchId;
      }

      // Execute queries in parallel for better performance
      const [branches, leaveData] = await Promise.all([
        Branch.findAll({
          where: branchWhere,
          attributes: ["id", "branch_name"],
          raw: true,
        }),
        UserRequest.findAll({
          where: {
            request_type: "casual",
            request_status: request_status.APPROVED,
          },
          include: [
            {
              model: User,
              as: "request_from_users",
              where: userWhere,
              attributes: ["branch_id"],
              required: true,
            },
          ],
          attributes: ["leave_days", "createdAt"],
          raw: true,
        }),
      ]);

      if (branches.length === 0) {
        return {
          success: true,
          chartType: "line",
          title: "Leave Comparison by Branch",
          data: {
            data: [],
            xAxisKey: "name",
            yAxisKey: "value",
            series: [],
          } as RechartsLineBarData,
          metadata: {
            totalRecords: 0,
            lastUpdated: DateUtils.now(),
          },
        };
      }

      // Create efficient lookup structures
      const leaveByBranchAndMonth = new Map<string, Map<string, number>>();

      // Initialize data structure
      branches.forEach((branch: any) => {
        const monthMap = new Map<string, number>();
        monthKeys.forEach((key) => monthMap.set(key, 0));
        leaveByBranchAndMonth.set(branch.id.toString(), monthMap);
      });

      // Single pass through leave data to aggregate using Moment.js
      leaveData.forEach((leave: any) => {
        if (leave.createdAt && leave["request_from_users.branch_id"]) {
          // Filter by date range using DateUtils
          if (DateUtils.isWithinLastMonths(leave.createdAt, 6)) {
            const monthKey = DateUtils.toMonthKey(leave.createdAt);
            const branchId = leave["request_from_users.branch_id"].toString();
            const branchData = leaveByBranchAndMonth.get(branchId);

            if (branchData && branchData.has(monthKey)) {
              const currentTotal = branchData.get(monthKey) || 0;
              branchData.set(monthKey, currentTotal + (leave.leave_days || 0));
            }
          }
        }
      });

      // Format data for Recharts multi-line chart
      const chartData: RechartsDataPoint[] = monthLabels.map((label, index) => {
        const dataPoint: RechartsDataPoint = {
          name: label,
          value: 0, // Will be calculated as sum of all branches
          month: label,
        };

        // Add each branch's data as a separate property
        branches.forEach((branch: any) => {
          const branchData = leaveByBranchAndMonth.get(branch.id.toString());
          const branchValue = branchData?.get(monthKeys[index]) || 0;
          dataPoint[`branch_${branch.id}`] = branchValue;
          dataPoint[branch.branch_name] = branchValue;
          dataPoint.value += branchValue;
        });

        return dataPoint;
      });

      // Generate series for each branch
      const colors = [
        "#4F46E5",
        "#10B981",
        "#F59E0B",
        "#EF4444",
        "#8B5CF6",
        "#06B6D4",
      ];
      const series = branches.map((branch: any, index: number) => ({
        dataKey: branch.branch_name,
        name: branch.branch_name,
        color: colors[index % colors.length],
        type: "line" as const,
      }));

      const totalRecords = leaveData.length;

      return {
        success: true,
        chartType: "line",
        title: "Leave Comparison by Branch",
        data: {
          data: chartData,
          xAxisKey: "name",
          yAxisKey: "value",
          series,
        } as RechartsLineBarData,
        metadata: {
          totalRecords,
          dateRange: `${monthLabels[0]} - ${monthLabels[monthLabels.length - 1]}`,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getLeaveComparisonByBranchData:", error);
      return {
        success: false,
        chartType: "line",
        title: "Leave Comparison by Branch",
        data: {
          data: [],
          xAxisKey: "name",
          yAxisKey: "value",
          series: [],
        } as RechartsLineBarData,
        metadata: {
          totalRecords: 0,
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

export interface DashboardWidget {
  id: number;
  title: string;
  type: string;
  data: any;
  category: "count" | "chart";
  chartType?: "line" | "bar" | "pie";
  order: number;
}

/**
 * Chart configuration mapping system
 * This eliminates hardcoded array indices and makes the system more maintainable
 */
const CHART_CONFIGURATIONS: Record<ChartType, ChartConfig> = {
  [ChartType.ONBOARDING_LINE]: {
    type: ChartType.ONBOARDING_LINE,
    widgetType: "line_chart",
    title: "Onboarding Pipeline Status by Branch",
    chartType: "line",
    category: "chart",
    description:
      "Shows onboarding pipeline status by branch with 2 series: Completed and Verified users",
    hasFilters: true,
    dataFetcher: getOnboardingPipelineLineData,
  },
  [ChartType.ONBOARDING_BAR]: {
    type: ChartType.ONBOARDING_BAR,
    widgetType: "bar_chart",
    title: "Onboarding Pipeline Status by Branch",
    chartType: "bar",
    category: "chart",
    description:
      "Compare onboarding status across branches with 3 series: Pending, Completed, and Verified users",
    hasFilters: false,
    dataFetcher: (organizationId: string) =>
      getOnboardingPipelineBarData(organizationId),
  },
  [ChartType.CONTRACT_PIE]: {
    type: ChartType.CONTRACT_PIE,
    widgetType: "pie_chart",
    title: "User Contract Distribution",
    chartType: "pie",
    category: "chart",
    description: "Distribution of user contract statuses",
    hasFilters: true,
    dataFetcher: getUserContractPieData,
  },
  [ChartType.LEAVE_COMPARISON]: {
    type: ChartType.LEAVE_COMPARISON,
    widgetType: "line_chart",
    title: "Leave Comparison by Branch",
    chartType: "line",
    category: "chart",
    description: "Monthly leave data comparison across branches",
    hasFilters: true,
    dataFetcher: getLeaveComparisonByBranchData,
  },
};

/**
 * Get chart configurations based on filters
 */
const getFilteredChartConfigs = (filters?: FilterOptions): ChartConfig[] => {
  let configs = Object.values(CHART_CONFIGURATIONS);

  // Filter by chart type if specified
  if (filters?.chartType) {
    configs = configs.filter(
      (config) => config.chartType === filters.chartType
    );
  }

  return configs;
};

/**
 * Create chart widget from configuration and data
 */
const createChartWidget = (
  config: ChartConfig,
  chartData: CommonChartResponse,
  index: number
): DashboardWidget => {
  // Generate dynamic ID based on chart type and index
  const baseId = 5; // Start chart IDs after the 4 count widgets
  const widgetId = baseId + index;

  return {
    id: widgetId,
    title: chartData.title || config.title,
    type: config.widgetType,
    data: {
      // Use the data from chartData.data directly to avoid duplication
      ...chartData.data,
      ...(config.hasFilters && { filters: { branchFilter: true } }),
      description: config.description,
    },
    category: config.category,
    chartType: config.chartType,
    order: widgetId, // Add the missing order property
  };
};

/**
 * Interface for User Statistics
 */
export interface UserStatistics {
  totalUsers: number;
  pendingUsers: number;
  activeUsers: number;
  ongoingUsers: number;
  completedUsers: number;
  verifiedUsers: number;
  deletedUsers: number;
}

/**
 * Interface for Employee Leave Statistics
 */
export interface EmployeeLeaveStatistics {
  employeesOnLeaveToday: number;
  employeesWithRequestedLeave: number;
}

/**
 * Interface for Change Request Statistics
 */
export interface ChangeRequestStatistics {
  totalChangeRequests: number;
  pendingChangeRequests: number;
  approvedChangeRequests: number;
  rejectedChangeRequests: number;
}

/**
 * Interface for Resignation Statistics
 */
export interface ResignationStatistics {
  totalResignations: number;
  pendingResignations: number;
  approvedResignations: number;
  processedResignations: number;
}

//  Interface for active branch & department statistics
export interface BranchDepartmentStatistics {
  activeBranches: number;
  activeDepartments: number;
}

/**
 * Interface for Rota Statistics
 */
export interface RotaStatistics {
  shiftSwapCount: number;
  dropShiftCount: number;
  totalShiftCount: number;
}

/**
 * Interface for Department Statistics (Simple number cards)
 */
export interface DepartmentStatistics {
  totalDepartmentCount: number;
  totalContractAssigned: number;
  totalContractUnassigned: number;
}

/**
 * Interface for Branch Statistics (Simple number cards)
 */
export interface BranchStatistics {
  totalBranchCount: number;
  totalDocumentAssigned: number;
  totalDocumentUnassigned: number;
}

/**
 * Interface for Complete Dashboard Statistics
 */
export interface DashboardStatistics {
  userStats: UserStatistics;
  leaveStats: EmployeeLeaveStatistics;
  changeRequestStats: ChangeRequestStatistics;
  resignationStats: ResignationStatistics;
  rotaStats: RotaStatistics;
  branchDeptStats: BranchDepartmentStatistics;
  departmentStats: DepartmentStatistics;
  branchStats: BranchStatistics;
}

/**
 * Get user-specific dashboard widgets
 * Returns widgets relevant to regular users with comprehensive statistics
 */
export const getUserDashboardWidgets = async (
  organizationId: string,
  userId: number,
  filters?: FilterOptions
): Promise<DashboardWidget[]> => {
  // Validate input parameters
  validateParameters(organizationId, userId);

  try {
    // Static count widgets (no database calls needed)
    const countWidgets: DashboardWidget[] = [
      {
        id: 1,
        title: "My Tasks",
        type: "task_summary",
        data: {
          pending: 5,
          completed: 12,
          overdue: 2,
        },
        order: 1,
        category: "count",
      },
      {
        id: 2,
        title: "My Leave Balance",
        type: "leave_balance",
        data: {
          available: 15,
          used: 10,
          pending: 2,
        },
        order: 2,
        category: "count",
      },
      {
        id: 3,
        title: "My Recent Activities",
        type: "activity_feed",
        data: {
          activities: [
            {
              action: "DSR Submitted",
              date: DateUtils.formatForDisplay(moment().subtract(1, "day")),
              status: "completed",
            },
            {
              action: "Leave Request",
              date: DateUtils.formatForDisplay(moment().subtract(2, "days")),
              status: "pending",
            },
          ],
        },
        order: 3,
        category: "count",
      },
      {
        id: 4,
        title: "My Performance",
        type: "performance_chart",
        data: {
          currentMonth: 85,
          lastMonth: 78,
          trend: "up",
        },
        order: 4,
        category: "count",
      },
    ];

    // Get filtered chart configurations based on user filters
    const chartConfigs = getFilteredChartConfigs(filters);

    // Create data fetching promises dynamically based on configurations
    const chartDataPromises = chartConfigs.map((config) => ({
      type: config.type,
      promise: config.dataFetcher(organizationId, filters),
    }));

    // Use Promise.allSettled to handle partial failures gracefully
    const chartResults = await Promise.allSettled(
      chartDataPromises.map((item) => item.promise)
    );

    // Create chart widgets dynamically using the mapping system
    const chartWidgets: DashboardWidget[] = [];

    chartResults.forEach((result, index) => {
      const config = chartConfigs[index];
      const chartType = chartDataPromises[index].type;

      if (result.status === "fulfilled" && result.value.success) {
        // Create widget using the configuration and data
        const widget = createChartWidget(config, result.value, index);
        chartWidgets.push(widget);
      } else {
        // Log failed chart data fetches for monitoring
        const errorMessage =
          result.status === "rejected"
            ? result.reason
            : "Chart data fetch returned unsuccessful result";
        console.error(
          `Chart data fetch failed for ${chartType}:`,
          errorMessage
        );
      }
    });

    // Get comprehensive statistics for admin users
    let statisticsData = null;
    try {
      statisticsData = await getComprehensiveStatistics(organizationId);
    } catch (error) {
      console.error(
        "Failed to fetch statistics data, continuing with basic widgets:",
        error
      );
    }

    // Add user statistics widget if statistics data is available
    if (statisticsData) {
      countWidgets.push({
        id: 5,
        title: "User Statistics",
        type: "user_statistics",
        data: statisticsData.userStats,
        order: 5,
        category: "count",
      });

      // Add rota statistics widget
      countWidgets.push({
        id: 6,
        title: "Rota Statistics",
        type: "rota_statistics",
        data: statisticsData.rotaStats,
        order: 6,
        category: "count",
      });
      // Add branch & department statistics widget
      countWidgets.push({
        id: 7,
        title: "Branch & Department Statistics",
        type: "branch_department_statistics",
        data: statisticsData.branchDeptStats,
        order: 7,
        category: "count",
      });

      // Add department statistics widget (Simple number cards)
      countWidgets.push({
        id: 8,
        title: "Departments",
        type: "department_statistics",
        data: {
          totalDepartmentCount:
            statisticsData.departmentStats.totalDepartmentCount,
          totalContractAssigned:
            statisticsData.departmentStats.totalContractAssigned,
          totalContractUnassigned:
            statisticsData.departmentStats.totalContractUnassigned,
        },
        order: 8,
        category: "count",
      });

      // Add branch statistics widget (Simple number cards)
      countWidgets.push({
        id: 9,
        title: "Branches",
        type: "branch_statistics",
        data: {
          totalBranchCount: statisticsData.branchStats.totalBranchCount,
          totalDocumentAssigned:
            statisticsData.branchStats.totalDocumentAssigned,
          totalDocumentUnassigned:
            statisticsData.branchStats.totalDocumentUnassigned,
        },
        order: 9,
        category: "count",
      });
    }

    // Combine widgets based on filter requirements
    let allWidgets: DashboardWidget[] = [];

    if (filters?.widgetType === "charts") {
      allWidgets = chartWidgets;
    } else if (filters?.widgetType === "counts") {
      allWidgets = countWidgets;
    } else {
      // Default: return both count and chart widgets
      allWidgets = [...countWidgets, ...chartWidgets];
    }

    // Apply chart type filtering if specified
    if (filters?.widgetType === "charts" && filters?.chartType) {
      allWidgets = allWidgets.filter(
        (widget) => widget.chartType === filters.chartType
      );
    }

    return allWidgets;
  } catch (error) {
    console.error("Error in getUserDashboardWidgets:", error);
    // Return at least the count widgets on error to provide partial functionality
    if (filters?.widgetType !== "charts") {
      return [
        {
          id: 1,
          title: "My Tasks",
          type: "task_summary",
          data: { pending: 0, completed: 0, overdue: 0 },
          category: "count",
          order: 1,
        },
      ];
    }
    throw error;
  }
};

/**
 * Get admin-specific dashboard widgets
 * Returns widgets relevant to administrators
 */
export const getDsrDashboardWidgets = async (
  organizationId: string,
  userId: number,
  filters?: FilterOptions
): Promise<DashboardWidget[]> => {
  try {
    const widgets: DashboardWidget[] = [];

    // Get comprehensive statistics for admin users
    let statisticsData = null;
    try {
      statisticsData = await getComprehensiveStatistics(organizationId);
    } catch (error) {
      console.warn(
        "Failed to fetch statistics data, continuing with basic widgets:",
        error
      );
    }

    // DSR-specific widgets
    widgets.push({
      id: 1,
      title: "DSR Summary",
      type: "dsr_stats",
      data: {
        totalDSRs: 150,
        pendingDSRs: 25,
        approvedDSRs: 120,
        rejectedDSRs: 5,
      },
      order: 1,
      category: "count",
    });

    widgets.push({
      id: 2,
      title: "Recent DSR Activities",
      type: "dsr_activities",
      data: {
        recentSubmissions: 12,
        pendingApprovals: 8,
        todaysSubmissions: 5,
      },
      order: 2,
      category: "count",
    });

    widgets.push({
      id: 3,
      title: "DSR Performance",
      type: "dsr_performance",
      data: {
        onTimeSubmissions: 85,
        lateSubmissions: 10,
        averageRating: 4.2,
      },
      order: 3,
      category: "count",
    });

    widgets.push({
      id: 4,
      title: "Team DSR Status",
      type: "team_dsr_status",
      data: {
        totalTeamMembers: 15,
        submittedToday: 12,
        pendingToday: 3,
      },
      order: 4,
      category: "count",
    });

    // Add employee leave statistics widget for admin dashboard
    if (statisticsData) {
      widgets.push({
        id: 5,
        title: "Employee Leave Status",
        type: "employee_leave_statistics",
        data: statisticsData.leaveStats, // Direct object assignment
        order: 5,
        category: "count",
      });
    }

    return widgets;
  } catch (error) {
    console.error("Error in getDsrDashboardWidgets:", error);
    throw error;
  }
};

/**
 * Get setup-specific dashboard widgets
 * Returns widgets relevant to system setup
 */
export const getSetupDashboardWidgets = async (
  organizationId: string,
  userId: number,
  filters?: FilterOptions
): Promise<DashboardWidget[]> => {
  try {
    const widgets: DashboardWidget[] = [];

    // Get comprehensive statistics for setup dashboard
    let statisticsData = null;
    try {
      statisticsData = await getComprehensiveStatistics(organizationId);
    } catch (error) {
      console.warn(
        "Failed to fetch statistics data, continuing with basic widgets:",
        error
      );
    }

    // Setup-specific widgets
    widgets.push({
      id: 1,
      title: "System Configuration",
      type: "system_config",
      data: {
        completedSteps: 8,
        totalSteps: 12,
        progressPercentage: 67,
      },
      order: 1,
      category: "count",
    });

    widgets.push({
      id: 2,
      title: "User Setup",
      type: "user_setup",
      data: {
        totalUsers: 25,
        activeUsers: 20,
        pendingInvitations: 5,
      },
      order: 2,
      category: "count",
    });

    widgets.push({
      id: 3,
      title: "Module Configuration",
      type: "module_config",
      data: {
        enabledModules: 8,
        totalModules: 12,
        pendingConfiguration: 4,
      },
      order: 3,
      category: "count",
    });

    widgets.push({
      id: 4,
      title: "Integration Status",
      type: "integration_status",
      data: {
        connectedServices: 3,
        totalServices: 6,
        failedConnections: 1,
      },
      order: 4,
      category: "count",
    });

    // Add change request and resignation statistics for setup dashboard
    if (statisticsData) {
      widgets.push({
        id: 5,
        title: "Change Requests",
        type: "change_request_statistics",
        data: statisticsData.changeRequestStats,
        order: 5,
        category: "count",
      });

      widgets.push({
        id: 6,
        title: "Resignations",
        type: "resignation_statistics",
        data: statisticsData.resignationStats,
        order: 6,
        category: "count",
      });
    }

    return widgets;
  } catch (error) {
    console.error("Error in getSetupDashboardWidgets:", error);
    throw error;
  }
};

/**
 * Get department statistics (Active departments + Contract counts)
 */
const getDepartmentStatistics = async (
  organizationId: string
): Promise<DepartmentStatistics> => {
  try {
    // Get total department count
    const totalDepartmentCount = await Department.count({
      where: {
        organization_id: organizationId,
        department_status: "active",
      },
    });

    // Get total users in organization
    const totalUsers = await User.count({
      where: {
        organization_id: organizationId,
      },
    });

    // Get total contract assigned count
    const totalContractAssigned = await UserEmploymentContract.count({
      include: [
        {
          model: User,
          as: "user_employment_contract",
          where: {
            organization_id: organizationId,
          },
          attributes: [],
          required: true,
        },
      ],
    });

    // Calculate unassigned contracts (users without contracts)
    const totalContractUnassigned = totalUsers - totalContractAssigned;

    return {
      totalDepartmentCount,
      totalContractAssigned,
      totalContractUnassigned,
    };
  } catch (error: any) {
    console.error("Error fetching department statistics:", error);
    console.error(
      "Department statistics error details:",
      error?.message || error
    );
    return {
      totalDepartmentCount: 0,
      totalContractAssigned: 0,
      totalContractUnassigned: 0,
    };
  }
};

/**
 * Get branch statistics (Active branches + Document counts)
 */
const getBranchStatistics = async (
  organizationId: string
): Promise<BranchStatistics> => {
  try {
    // Get total branch count
    const totalBranchCount = await Branch.count({
      where: {
        organization_id: organizationId,
        branch_status: "active",
      },
    });

    // Get total users in organization
    const totalUsers = await User.count({
      where: {
        organization_id: organizationId,
      },
    });

    // Note: We no longer need total document categories count
    // since we're counting based on branch assignments

    // Get total document assigned count using branch assignments
    const documentAssignedResult = await sequelize.query(
      `SELECT COUNT(*) as count
       FROM nv_document_category_branch dcb
       INNER JOIN nv_document_category dc ON dcb.category_id = dc.id
       WHERE dc.organization_id = :organizationId
       AND dcb.document_category_branch_status = 'active'`,
      {
        replacements: { organizationId },
        type: QueryTypes.SELECT,
      }
    );
    const totalDocumentAssigned = (documentAssignedResult[0] as any).count;

    // Calculate unassigned documents based on branch assignments
    // Get total users in branches that have document assignments
    const usersInAssignedBranchesResult = await sequelize.query(
      `SELECT COUNT(DISTINCT u.id) as count
       FROM nv_users u
       INNER JOIN nv_document_category_branch dcb ON u.branch_id = dcb.branch_id
       INNER JOIN nv_document_category dc ON dcb.category_id = dc.id
       WHERE dc.organization_id = :organizationId
       AND dcb.document_category_branch_status = 'active'
       AND u.organization_id = :organizationId`,
      {
        replacements: { organizationId },
        type: QueryTypes.SELECT,
      }
    );
    const usersInAssignedBranches = (usersInAssignedBranchesResult[0] as any)
      .count;

    // Calculate unassigned as users who could have assignments but don't
    const totalDocumentUnassigned = Math.max(
      0,
      totalUsers - usersInAssignedBranches
    );

    return {
      totalBranchCount,
      totalDocumentAssigned,
      totalDocumentUnassigned,
    };
  } catch (error: any) {
    console.error("Error fetching branch statistics:", error);
    console.error("Branch statistics error details:", error?.message || error);
    return {
      totalBranchCount: 0,
      totalDocumentAssigned: 0,
      totalDocumentUnassigned: 0,
    };
  }
};

/**
 * Get comprehensive statistics data for dashboard widgets
 * This function aggregates all statistics in one place following the existing pattern
 */
const getComprehensiveStatistics = async (organizationId: string) => {
  try {
    // Execute all statistics queries in parallel for better performance
    const [
      userStats,
      leaveStats,
      changeRequestStats,
      resignationStats,
      rotaStats,
      branchDeptStats,
      departmentStats,
      branchStats,
    ] = await Promise.all([
      getUserStatistics(organizationId),
      getEmployeeLeaveStatistics(organizationId),
      getChangeRequestStatistics(organizationId),
      getResignationStatistics(organizationId),
      getRotaStatistics(organizationId),
      getBranchDepartmentStatistics(organizationId),
      getDepartmentStatistics(organizationId),
      getBranchStatistics(organizationId),
    ]);

    return {
      userStats,
      leaveStats,
      changeRequestStats,
      resignationStats,
      rotaStats,
      branchDeptStats,
      departmentStats,
      branchStats,
    };
  } catch (error) {
    console.error("Error in getComprehensiveStatistics:", error);
    throw error;
  }
};

/**
 * Get user statistics for the organization
 */
const getUserStatistics = async (
  organizationId: string
): Promise<UserStatistics> => {
  try {
    // fetch user statistics
    const userStatsQuery = await User.findOne({
      attributes: [
        [sequelize.fn("COUNT", sequelize.col("*")), "total_users"],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.PENDING}' THEN 1 ELSE 0 END`
            )
          ),
          "total_pending",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.ACTIVE}' THEN 1 ELSE 0 END`
            )
          ),
          "total_active",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.ONGOING}' THEN 1 ELSE 0 END`
            )
          ),
          "total_ongoing",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.COMPLETED}' THEN 1 ELSE 0 END`
            )
          ),
          "total_completed",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.VERIFIED}' THEN 1 ELSE 0 END`
            )
          ),
          "total_verified",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.DELETED}' THEN 1 ELSE 0 END`
            )
          ),
          "total_deleted",
        ],
      ],
      where: {
        organization_id: organizationId,
      },
      raw: true,
      subQuery: false,
    });

    return {
      totalUsers: parseInt((userStatsQuery as any)?.total_users as string) || 0,
      pendingUsers:
        parseInt((userStatsQuery as any)?.total_pending as string) || 0,
      activeUsers:
        parseInt((userStatsQuery as any)?.total_active as string) || 0,
      ongoingUsers:
        parseInt((userStatsQuery as any)?.total_ongoing as string) || 0,
      completedUsers:
        parseInt((userStatsQuery as any)?.total_completed as string) || 0,
      verifiedUsers:
        parseInt((userStatsQuery as any)?.total_verified as string) || 0,
      deletedUsers:
        parseInt((userStatsQuery as any)?.total_deleted as string) || 0,
    };
  } catch (error) {
    console.error("Error fetching user statistics:", error);
    // Return default values on error to prevent dashboard from breaking
    return {
      totalUsers: 0,
      pendingUsers: 0,
      activeUsers: 0,
      ongoingUsers: 0,
      completedUsers: 0,
      verifiedUsers: 0,
      deletedUsers: 0,
    };
  }
};

/**
 * Get employee leave statistics for the organization
 */
const getEmployeeLeaveStatistics = async (
  organizationId: string
): Promise<EmployeeLeaveStatistics> => {
  try {
    const today = moment().format("YYYY-MM-DD");

    // Count employees on leave today (approved leave requests that include today's date)
    const employeesOnLeaveToday = await UserRequest.count({
      include: [
        {
          model: User,
          as: "request_from_users", // Use the proper association alias
          where: {
            organization_id: organizationId,
          },
          attributes: [], // Don't select user attributes for better performance
        },
      ],
      where: {
        request_status: "approved",
        start_date: { [Op.lte]: today },
        end_date: { [Op.gte]: today },
      },
    });

    // Count employees with pending leave requests
    const employeesWithRequestedLeave = await UserRequest.count({
      include: [
        {
          model: User,
          as: "request_from_users", // Use the proper association alias
          where: {
            organization_id: organizationId,
          },
          attributes: [], // Don't select user attributes for better performance
        },
      ],
      where: {
        request_status: "pending",
      },
    });

    return {
      employeesOnLeaveToday,
      employeesWithRequestedLeave,
    };
  } catch (error) {
    console.error("Error fetching employee leave statistics:", error);
    // Return default values on error
    return {
      employeesOnLeaveToday: 0,
      employeesWithRequestedLeave: 0,
    };
  }
};

/**
 * Get change request statistics for the organization
 */
const getChangeRequestStatistics = async (
  organizationId: string
): Promise<ChangeRequestStatistics> => {
  try {
    const changeRequestStatsQuery = await ChangeRequest.findAll({
      attributes: [
        [sequelize.fn("COUNT", sequelize.col("*")), "totalChangeRequests"],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN change_request_status = 'pending' THEN 1 ELSE 0 END`
            )
          ),
          "pendingChangeRequests",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN change_request_status = 'approved' THEN 1 ELSE 0 END`
            )
          ),
          "approvedChangeRequests",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN change_request_status = 'rejected' THEN 1 ELSE 0 END`
            )
          ),
          "rejectedChangeRequests",
        ],
      ],
      include: [
        {
          model: User,
          as: "change_request_user", // Use the proper association alias
          where: { organization_id: organizationId },
          attributes: [], // Don't select user attributes to avoid GROUP BY issues
        },
      ],
      raw: true,
    });

    const stats = changeRequestStatsQuery[0] as any;

    return {
      totalChangeRequests: parseInt(stats?.totalChangeRequests) || 0,
      pendingChangeRequests: parseInt(stats?.pendingChangeRequests) || 0,
      approvedChangeRequests: parseInt(stats?.approvedChangeRequests) || 0,
      rejectedChangeRequests: parseInt(stats?.rejectedChangeRequests) || 0,
    };
  } catch (error) {
    console.error("Error fetching change request statistics:", error);
    // Return default values on error
    return {
      totalChangeRequests: 0,
      pendingChangeRequests: 0,
      approvedChangeRequests: 0,
      rejectedChangeRequests: 0,
    };
  }
};

/**
 * Get resignation statistics for the organization
 */
const getResignationStatistics = async (
  organizationId: string
): Promise<ResignationStatistics> => {
  try {
    // Use simpler approach without JOIN to avoid GROUP BY issues
    const resignationStatsQuery = await Resignation.findAll({
      attributes: [
        [sequelize.fn("COUNT", sequelize.col("*")), "totalResignations"],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN resignation_status = 'pending' THEN 1 ELSE 0 END`
            )
          ),
          "pendingResignations",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN resignation_status = 'accepted' THEN 1 ELSE 0 END`
            )
          ),
          "approvedResignations",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN resignation_status = 'accepted' OR resignation_status = 'in-discussion' THEN 1 ELSE 0 END`
            )
          ),
          "processedResignations",
        ],
      ],
      include: [
        {
          model: User,
          as: "resign_user",
          where: { organization_id: organizationId },
          attributes: [],
        },
      ],
      raw: true,
    });

    const stats = resignationStatsQuery[0] as any;

    return {
      totalResignations: parseInt(stats?.totalResignations) || 0,
      pendingResignations: parseInt(stats?.pendingResignations) || 0,
      approvedResignations: parseInt(stats?.approvedResignations) || 0,
      processedResignations: parseInt(stats?.processedResignations) || 0,
    };
  } catch (error) {
    console.error("Error fetching resignation statistics:", error);
    // Return default values on error
    return {
      totalResignations: 0,
      pendingResignations: 0,
      approvedResignations: 0,
      processedResignations: 0,
    };
  }
};

/**
 * Get rota (shift) statistics for the organization
 * - shiftSwapCount  : Shifts that are marked as swap (isSwap = true) and not deleted
 * - dropShiftCount  : Shifts that are marked as dropped (isDropped = true)
 * - totalShiftCount : All shifts that are not deleted
 */
const getRotaStatistics = async (
  organizationId: string
): Promise<RotaStatistics> => {
  try {
    // Shift swap count (isSwap = true and status != deleted)
    const [swapResult]: any = await sequelize.query(
      `SELECT COUNT(id) AS count FROM shifts WHERE isSwap = 1 AND status <> 'deleted' AND organization_id = :orgId`,
      {
        replacements: { orgId: organizationId },
        type: QueryTypes.SELECT,
      }
    );

    // Dropped shift count (isDropped = true and status != deleted)
    const [dropResult]: any = await sequelize.query(
      `SELECT COUNT(id) AS count FROM shifts WHERE isDropped = 1  AND organization_id = :orgId`,
      {
        replacements: { orgId: organizationId },
        type: QueryTypes.SELECT,
      }
    );

    // Total shifts (status != deleted)
    const [totalResult]: any = await sequelize.query(
      `SELECT COUNT(id) AS count FROM shifts WHERE status <> 'deleted' AND organization_id = :orgId`,
      {
        replacements: { orgId: organizationId },
        type: QueryTypes.SELECT,
      }
    );

    return {
      shiftSwapCount: parseInt(swapResult?.count as string) || 0,
      dropShiftCount: parseInt(dropResult?.count as string) || 0,
      totalShiftCount: parseInt(totalResult?.count as string) || 0,
    } as RotaStatistics;
  } catch (error) {
    console.error("Error fetching rota statistics:", error);
    // Return default values on error to prevent dashboard from breaking
    return {
      shiftSwapCount: 0,
      dropShiftCount: 0,
      totalShiftCount: 0,
    };
  }
};

/**
 * Get active branch and department counts for the organization
 */
const getBranchDepartmentStatistics = async (
  organizationId: string
): Promise<BranchDepartmentStatistics> => {
  try {
    const [activeBranches, activeDepartments] = await Promise.all([
      Branch.count({
        where: { organization_id: organizationId, branch_status: "active" },
      }),
      Department.count({
        where: { organization_id: organizationId, department_status: "active" },
      }),
    ]);

    return {
      activeBranches,
      activeDepartments,
    } as BranchDepartmentStatistics;
  } catch (error) {
    console.error("Error fetching branch/department statistics:", error);
    return { activeBranches: 0, activeDepartments: 0 };
  }
};

export default {
  getUserDashboardWidgets,
  getDsrDashboardWidgets,
  getSetupDashboardWidgets,
};
