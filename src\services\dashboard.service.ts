import {
  UserEmploymentContract,
  contract_status,
} from "../models/UserEmployementContract";
import { UserRequest, request_status } from "../models/UserRequest";
import { UserMeta } from "../models/UserMeta";
import { Branch, branch_status } from "../models/Branch";
import { Department } from "../models/Department";
import { Role } from "../models/Role";
import { Role as MORole } from "../models/MORole";
import { LeaveTypeModel } from "../models/LeaveType";
import { LeavePolicyModel } from "../models/LeavePolicy";
import { DocumentCategory } from "../models/DocumentCategory";
import { Item, item_status } from "../models/Item";
import { HolidayPolicy } from "../models/HolidayPolicy";
import { HolidayType } from "../models/HolidayType";
import moment from "moment";

// Configure moment.js for consistent behavior
moment.locale("en"); // Set default locale
import { User, user_status } from "../models/User";
import { ChangeRequest } from "../models/ChangeRequest";
import { Resignation } from "../models/Resignation";
import { sequelize } from "../models";
import { Op, QueryTypes } from "sequelize";

/**
 * Dashboard Service
 * Contains data functions for retrieving dashboard widgets
 */

// Enums for chart identification
enum ChartType {
  ONBOARDING_PIE = "onboarding_pie",
  ONBOARDING_BAR = "onboarding_bar",
  CONTRACT_GAUGE = "contract_gauge",
  LEAVE_COMPARISON = "leave_comparison",
}

// Types for Recharts compatibility
interface RechartsDataPoint {
  name: string;
  value: number;
  [key: string]: any; // Allow additional properties for multi-series data
}

interface RechartsLineBarData {
  data: RechartsDataPoint[];
  xAxisKey: string;
  yAxisKey: string;
  series: {
    dataKey: string;
    name: string;
    color: string;
    type?: "line" | "bar";
  }[];
}

interface RechartsPieData {
  data: RechartsDataPoint[];
  nameKey: string;
  valueKey: string;
  colors: string[];
}

interface CommonChartResponse {
  success: boolean;
  chartType: "line" | "bar" | "pie" | "gauge";
  title: string;
  data: RechartsLineBarData | RechartsPieData | any; // Added 'any' for gauge data
  metadata?: {
    totalRecords: number;
    dateRange?: string;
    timelinePeriod?: string;
    branchCount?: number;
    filters?: any;
    lastUpdated: string;
    [key: string]: any; // Allow additional metadata for gauge charts
  };
}

// Chart configuration interface
interface ChartConfig {
  type: ChartType;
  widgetType: string;
  title: string;
  chartType: "line" | "bar" | "pie" | "gauge";
  category: "chart";
  description: string;
  hasFilters: boolean;
  dataFetcher: (
    organizationId: string,
    filters?: FilterOptions
  ) => Promise<CommonChartResponse>;
}

interface FilterOptions {
  branchId?: number;
  widgetType?: "counts" | "charts" | "all";
  chartType?: "line" | "bar" | "pie" | "gauge";
  activity_action?: "created" | "updated" | "login" | "logout";
  notification_status?: "read" | "pending";
}

// Cache for frequently accessed data (in production, use Redis or similar)
const dataCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get cached data or fetch if expired
 */
const getCachedData = async <T>(
  key: string,
  fetchFn: () => Promise<T>,
  ttl: number = CACHE_TTL
): Promise<T> => {
  const cached = dataCache.get(key);
  const now = Date.now();

  if (cached && now - cached.timestamp < ttl) {
    return cached.data as T;
  }

  const data = await fetchFn();
  dataCache.set(key, { data, timestamp: now });
  return data;
};

/**
 * Validate required parameters
 */
const validateParameters = (organizationId: string, userId: number): void => {
  if (!organizationId || typeof organizationId !== "string") {
    throw new Error("Invalid organizationId: must be a non-empty string");
  }
  if (!userId || typeof userId !== "number" || userId <= 0) {
    throw new Error("Invalid userId: must be a positive number");
  }
};

/**
 * Generate date range for the last N months using Moment.js
 */
const generateMonthRange = (
  months: number
): { start: Date; monthKeys: string[]; monthLabels: string[] } => {
  const start = moment().subtract(months, "months").startOf("month").toDate();

  const monthKeys: string[] = [];
  const monthLabels: string[] = [];

  for (let i = months - 1; i >= 0; i--) {
    const monthMoment = moment().subtract(i, "months");
    monthKeys.push(monthMoment.format("YYYY-MM"));
    monthLabels.push(monthMoment.format("MMM YYYY"));
  }

  return { start, monthKeys, monthLabels };
};

/**
 * Utility functions for consistent date handling with Moment.js
 */
const DateUtils = {
  /**
   * Validate and format date to YYYY-MM format for month grouping
   */
  toMonthKey: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to toMonthKey: ${date}`);
      return moment().format("YYYY-MM"); // Return current month as fallback
    }
    return momentDate.format("YYYY-MM");
  },

  /**
   * Format date to human-readable month label
   */
  toMonthLabel: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to toMonthLabel: ${date}`);
      return moment().format("MMM YYYY"); // Return current month as fallback
    }
    return momentDate.format("MMM YYYY");
  },

  /**
   * Check if date is within the last N months
   */
  isWithinLastMonths: (
    date: string | Date | moment.Moment,
    months: number
  ): boolean => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to isWithinLastMonths: ${date}`);
      return false;
    }
    const cutoffDate = moment().subtract(months, "months").startOf("month");
    return momentDate.isAfter(cutoffDate);
  },

  /**
   * Get start of month for a given date
   */
  startOfMonth: (date: string | Date | moment.Moment): Date => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to startOfMonth: ${date}`);
      return moment().startOf("month").toDate(); // Return current month start as fallback
    }
    return momentDate.startOf("month").toDate();
  },

  /**
   * Format date for display with validation
   */
  formatForDisplay: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to formatForDisplay: ${date}`);
      return moment().format("YYYY-MM-DD"); // Return current date as fallback
    }
    return momentDate.format("YYYY-MM-DD");
  },

  /**
   * Get relative time from now (e.g., "2 days ago")
   */
  fromNow: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to fromNow: ${date}`);
      return "Invalid date";
    }
    return momentDate.fromNow();
  },

  /**
   * Check if date is valid
   */
  isValid: (date: string | Date | moment.Moment): boolean => {
    return moment(date).isValid();
  },

  /**
   * Get current timestamp in ISO format
   */
  now: (): string => {
    return moment().toISOString();
  },
};

/**
 * Get onboarding pipeline status data for pie chart (Recharts format)
 * Based on getOnboardingPipelineBarData but converted to pie chart format
 * Shows distribution of Completed and Verified users across all branches
 * Supports branch filtering to show data for specific branch only
 */
const getOnboardingPipelinePieData = async (
  organizationId: string,
  filters?: FilterOptions
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `onboarding_pie_${organizationId}_${filters?.branchId || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build where clauses for branches and users
      const branchWhere: any = {
        organization_id: organizationId,
        branch_status: branch_status.ACTIVE,
      };

      const userWhere: any = {
        organization_id: organizationId,
      };

      // Apply branch filtering if specified
      if (filters?.branchId) {
        branchWhere.id = filters.branchId;
        userWhere.branch_id = filters.branchId;
      }

      // Execute queries in parallel for better performance
      const [branches, users] = await Promise.all([
        Branch.findAll({
          where: branchWhere,
          attributes: ["id", "branch_name"],
          raw: true,
        }),
        User.findAll({
          where: userWhere,
          attributes: ["id", "branch_id", "user_status"],
          raw: true,
        }),
      ]);

      if (branches.length === 0) {
        return {
          success: true,
          chartType: "pie",
          title: "Onboarding Pipeline Distribution",
          data: {
            data: [],
            nameKey: "name",
            valueKey: "value",
            colors: ["#10B981", "#8B5CF6"],
          } as RechartsPieData,
          metadata: {
            totalRecords: 0,
            lastUpdated: DateUtils.now(),
          },
        };
      }

      // Initialize status counters (only Completed and Verified as per line chart logic)
      const statusCounts = {
        Completed: 0,
        Verified: 0,
      };

      // Aggregate user counts by status (only Completed and Verified)
      users.forEach((user: any) => {
        switch (user.user_status) {
          case user_status.COMPLETED:
            statusCounts.Completed++;
            break;
          case user_status.VERIFIED:
            statusCounts.Verified++;
            break;
          // Skip pending, active, and ongoing statuses as per line chart logic
        }
      });

      // Define colors for each status
      const statusColors = {
        Completed: "#10B981", // Green
        Verified: "#8B5CF6", // Purple
      };

      // Format data for Recharts pie chart
      const chartData: RechartsDataPoint[] = Object.entries(statusCounts)
        .filter(([, count]) => count > 0) // Only include statuses with data
        .map(([status, count]) => ({
          name: status,
          value: count,
          status: status,
          count: count,
          color: statusColors[status as keyof typeof statusColors],
        }));

      const totalRecords = chartData.reduce((sum, item) => sum + item.value, 0);

      // Convert statusColors object to array for RechartsPieData compatibility
      const colorsArray = Object.values(statusColors);

      return {
        success: true,
        chartType: "pie",
        title: "Onboarding Pipeline Distribution",
        data: {
          data: chartData,
          nameKey: "name",
          valueKey: "value",
          colors: colorsArray,
        } as RechartsPieData,
        metadata: {
          totalRecords,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getOnboardingPipelinePieData:", error);
      return {
        success: false,
        chartType: "pie",
        title: "Onboarding Pipeline Distribution",
        data: {
          data: [],
          nameKey: "name",
          valueKey: "value",
          colors: ["#10B981", "#8B5CF6"],
        } as RechartsPieData,
        metadata: {
          totalRecords: 0,
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

/**
 * Get onboarding pipeline status data for bar chart (Recharts format)
 * Based on getOnboardingPipelineLineData but converted to bar chart format
 * X-axis: Branches, Y-axis: User counts
 * 2 data series: Completed, Verified (matching line chart logic)
 */
const getOnboardingPipelineBarData = async (
  organizationId: string,
  filters?: FilterOptions
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `onboarding_bar_${organizationId}_${filters?.branchId || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build where clauses for branches and users
      const branchWhere: any = {
        organization_id: organizationId,
        branch_status: branch_status.ACTIVE,
      };

      const userWhere: any = {
        organization_id: organizationId,
      };

      // Apply branch filtering if specified
      if (filters?.branchId) {
        branchWhere.id = filters.branchId;
        userWhere.branch_id = filters.branchId;
      }

      // Execute queries in parallel for better performance
      const [branches, users] = await Promise.all([
        Branch.findAll({
          where: branchWhere,
          attributes: ["id", "branch_name"],
          raw: true,
        }),
        User.findAll({
          where: userWhere,
          attributes: ["id", "branch_id", "user_status"],
          raw: true,
        }),
      ]);

      if (branches.length === 0) {
        return {
          success: true,
          chartType: "bar",
          title: "Onboarding Pipeline Status by Branch",
          data: {
            data: [],
            xAxisKey: "name",
            yAxisKey: "value",
            series: [
              {
                dataKey: "completed",
                name: "Completed",
                color: "#10B981",
                type: "bar",
              },
              {
                dataKey: "verified",
                name: "Verified",
                color: "#8B5CF6",
                type: "bar",
              },
            ],
          } as RechartsLineBarData,
          metadata: {
            totalRecords: 0,
            lastUpdated: DateUtils.now(),
          },
        };
      }

      // Initialize branch data structure (only completed and verified as per line chart)
      const branchData = new Map<
        number,
        {
          name: string;
          completed: number;
          verified: number;
        }
      >();

      // Initialize all branches with zero counts
      branches.forEach((branch: any) => {
        branchData.set(branch.id, {
          name: branch.branch_name,
          completed: 0,
          verified: 0,
        });
      });

      // Aggregate user counts by branch and status (only completed and verified)
      users.forEach((user: any) => {
        const branchInfo = branchData.get(user.branch_id);
        if (branchInfo) {
          switch (user.user_status) {
            case user_status.COMPLETED:
              branchInfo.completed++;
              break;
            case user_status.VERIFIED:
              branchInfo.verified++;
              break;
            // Skip pending, active, and ongoing statuses as per line chart logic
          }
        }
      });

      // Transform to Recharts format
      const chartData: RechartsDataPoint[] = Array.from(
        branchData.values()
      ).map((branch) => ({
        name: branch.name,
        value: branch.completed + branch.verified, // Total for reference
        completed: branch.completed,
        verified: branch.verified,
      }));

      const totalRecords = users.length;

      return {
        success: true,
        chartType: "bar",
        title: "Onboarding Pipeline Status by Branch",
        data: {
          data: chartData,
          xAxisKey: "name",
          yAxisKey: "value",
          series: [
            {
              dataKey: "completed",
              name: "Completed",
              color: "#10B981",
              type: "bar",
            },
            {
              dataKey: "verified",
              name: "Verified",
              color: "#8B5CF6",
              type: "bar",
            },
          ],
        } as RechartsLineBarData,
        metadata: {
          totalRecords,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getOnboardingPipelineBarData:", error);
      return {
        success: false,
        chartType: "bar",
        title: "Onboarding Pipeline Status by Branch",
        data: {
          data: [],
          xAxisKey: "name",
          yAxisKey: "value",
          series: [
            {
              dataKey: "completed",
              name: "Completed",
              color: "#10B981",
              type: "bar",
            },
            {
              dataKey: "verified",
              name: "Verified",
              color: "#8B5CF6",
              type: "bar",
            },
          ],
        } as RechartsLineBarData,
        metadata: {
          totalRecords: 0,
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

/**
 * Get user contract status data for gauge chart (Recharts format)
 * Based on getUserContractPieData but converted to gauge/meter chart format
 * Shows contract health metrics: percentage of healthy vs problematic contracts
 * Healthy: Confirmed + Probation
 * Problematic: Expired + Expiry Soon + Pending + Awaiting Signature
 */
const getUserContractGaugeData = async (
  organizationId: string,
  filters?: FilterOptions
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `contract_gauge_${organizationId}_${filters?.branchId || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build where clause for User table
      const userWhere: any = {
        organization_id: organizationId,
      };

      if (filters?.branchId) {
        userWhere.branch_id = filters.branchId;
      }

      // Get all users with their contracts and meta data for probation calculation
      const usersWithContracts = await User.findAll({
        include: [
          {
            model: UserEmploymentContract,
            as: "user_contract",
            attributes: ["contract_status", "expire_date", "is_confirm_sign"],
            where: {
              contract_status: contract_status.ACTIVE,
            },
            required: true,
          },
          {
            model: UserMeta,
            as: "user_meta",
            attributes: ["probation_length"],
            required: false,
          },
        ],
        attributes: ["id", "user_joining_date"],
        where: userWhere,
        raw: true,
        nest: true,
      });

      // Initialize status counters
      const statusCounts = {
        Expired: 0,
        "Expiry Soon": 0,
        Probation: 0,
        Confirmed: 0,
        Pending: 0,
        "Awaiting Signature": 0,
      };

      // Process each user and categorize based on frontend logic
      usersWithContracts.forEach((user: any) => {
        const currentDate = moment().startOf("day");
        const expireDate = moment(user.user_contract.expire_date).startOf(
          "day"
        );
        const daysDifference = expireDate.diff(currentDate, "days");

        // Determine probation status
        const joiningDate = moment(user.user_joining_date);
        const probationLength = user.user_meta?.probation_length || 0;
        const probationEndDate = joiningDate
          .clone()
          .add(probationLength, "days");
        const isProbation =
          probationLength > 0 && moment().isBefore(probationEndDate) ? 1 : 0;

        // Apply the same logic as frontend
        let status: string;

        if (daysDifference <= 0) {
          status = "Expired";
        } else if (daysDifference > 0 && daysDifference <= 15) {
          status = "Expiry Soon";
        } else if (
          user.user_contract.is_confirm_sign === true ||
          user.user_contract.is_confirm_sign === 1
        ) {
          status = isProbation === 1 ? "Probation" : "Confirmed";
        } else if (user.user_contract.is_confirm_sign === null) {
          status = "Pending";
        } else {
          status = "Awaiting Signature";
        }

        statusCounts[status as keyof typeof statusCounts]++;
      });

      // Calculate health metrics
      const healthyCount = statusCounts.Confirmed + statusCounts.Probation;
      const problematicCount =
        statusCounts.Expired +
        statusCounts["Expiry Soon"] +
        statusCounts.Pending +
        statusCounts["Awaiting Signature"];
      const totalCount = healthyCount + problematicCount;

      const healthPercentage =
        totalCount > 0 ? Math.round((healthyCount / totalCount) * 100) : 0;

      // Format data for gauge chart
      const gaugeData = [
        {
          name: "Contract Health",
          value: healthPercentage,
          healthy: healthyCount,
          problematic: problematicCount,
          total: totalCount,
          color:
            healthPercentage >= 80
              ? "#10B981"
              : healthPercentage >= 60
                ? "#F59E0B"
                : "#EF4444",
        },
      ];

      return {
        success: true,
        chartType: "gauge",
        title: "Contract Health Metrics",
        data: {
          data: gaugeData,
          nameKey: "name",
          valueKey: "value",
          maxValue: 100,
          unit: "%",
          colors: ["#EF4444", "#F59E0B", "#10B981"], // Red, Orange, Green
          thresholds: [
            { value: 60, color: "#EF4444", label: "Poor" },
            { value: 80, color: "#F59E0B", label: "Fair" },
            { value: 100, color: "#10B981", label: "Good" },
          ],
        },
        metadata: {
          totalRecords: totalCount,
          healthyContracts: healthyCount,
          problematicContracts: problematicCount,
          healthPercentage: healthPercentage,
          breakdown: statusCounts,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getUserContractGaugeData:", error);
      return {
        success: false,
        chartType: "gauge",
        title: "Contract Health Metrics",
        data: {
          data: [],
          nameKey: "name",
          valueKey: "value",
          maxValue: 100,
          unit: "%",
          colors: ["#EF4444", "#F59E0B", "#10B981"],
          thresholds: [],
        },
        metadata: {
          totalRecords: 0,
          healthyContracts: 0,
          problematicContracts: 0,
          healthPercentage: 0,
          breakdown: {
            Expired: 0,
            "Expiry Soon": 0,
            Probation: 0,
            Confirmed: 0,
            Pending: 0,
            "Awaiting Signature": 0,
          },
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

/**
 * Get leave comparison data by branch for line chart (Recharts format)
 * Shows 12-month timeline with each branch as a separate line
 * X-axis: 12-month timeline from current month back one year
 * Y-axis: Leave count values
 * Data series: One line per branch with branch colors from database
 */
const getLeaveComparisonByBranchData = async (
  organizationId: string,
  filters?: FilterOptions
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `leave_comparison_${organizationId}_${filters?.branchId || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Generate 12-month timeline starting from current month going back one year
      const { monthKeys, monthLabels } = generateMonthRange(12);

      // Build where clauses for branch filtering
      const branchWhere: any = {
        organization_id: organizationId,
        branch_status: branch_status.ACTIVE,
      };

      // Build where clauses for user filtering
      const userWhere: any = {
        organization_id: organizationId,
      };

      // Apply branch filtering if specified
      if (filters?.branchId) {
        branchWhere.id = filters.branchId;
        userWhere.branch_id = filters.branchId;
      }

      // Execute queries in parallel for better performance
      // Query ALL branches first (regardless of leave data), then query leave data separately
      const [branches, leaveData] = await Promise.all([
        // Query ALL active branches in organization (regardless of leave data)
        Branch.findAll({
          where: branchWhere,
          attributes: ["id", "branch_name", "branch_color"],
          raw: true,
        }),

        // Query leave requests with user branch information
        UserRequest.findAll({
          where: {
            request_type: "casual",
            request_status: request_status.APPROVED,
          },
          include: [
            {
              model: User,
              as: "request_from_users",
              where: userWhere,
              attributes: ["branch_id"],
              required: true,
            },
          ],
          attributes: ["leave_days", "createdAt"],
          raw: true,
        }),
      ]);

      if (branches.length === 0) {
        return {
          success: true,
          chartType: "line",
          title: "Leave Comparison by Branch (12-Month Timeline)",
          data: {
            data: [],
            xAxisKey: "name",
            yAxisKey: "value",
            series: [],
          } as RechartsLineBarData,
          metadata: {
            totalRecords: 0,
            timelinePeriod: "12 months",
            branchCount: 0,
            lastUpdated: DateUtils.now(),
          },
        };
      }

      // Create efficient lookup structures for 12-month data
      const leaveByBranchAndMonth = new Map<string, Map<string, number>>();

      // Initialize data structure - ensure all branches have data for all 12 months
      branches.forEach((branch: any) => {
        const monthMap = new Map<string, number>();
        monthKeys.forEach((key) => monthMap.set(key, 0));
        leaveByBranchAndMonth.set(branch.id.toString(), monthMap);
      });

      // Single pass through leave data to aggregate using Moment.js for 12-month period
      leaveData.forEach((leave: any) => {
        if (leave.createdAt && leave["request_from_users.branch_id"]) {
          // Filter by 12-month date range using DateUtils
          if (DateUtils.isWithinLastMonths(leave.createdAt, 12)) {
            const monthKey = DateUtils.toMonthKey(leave.createdAt);
            const branchId = leave["request_from_users.branch_id"].toString();
            const branchData = leaveByBranchAndMonth.get(branchId);

            if (branchData && branchData.has(monthKey)) {
              const currentTotal = branchData.get(monthKey) || 0;
              branchData.set(monthKey, currentTotal + (leave.leave_days || 0));
            }
          }
        }
      });

      // Format data for Recharts line chart - 12-month timeline with branch lines
      const chartData: RechartsDataPoint[] = monthLabels.map((label, index) => {
        const dataPoint: RechartsDataPoint = {
          name: label,
          value: 0, // Will be calculated as sum of all branches for total reference
          month: label,
        };

        // Add each branch's data as a separate property for line chart
        branches.forEach((branch: any) => {
          const branchData = leaveByBranchAndMonth.get(branch.id.toString());
          const branchValue = branchData?.get(monthKeys[index]) || 0;
          // Only use branch name as dataKey (remove redundant branch_id property)
          dataPoint[branch.branch_name] = branchValue;
          dataPoint.value += branchValue;
        });

        return dataPoint;
      });

      // Generate series for each branch using database colors
      const defaultColors = [
        "#4F46E5",
        "#10B981",
        "#F59E0B",
        "#EF4444",
        "#8B5CF6",
        "#06B6D4",
      ];
      const series = branches.map((branch: any, index: number) => ({
        dataKey: branch.branch_name,
        name: branch.branch_name,
        color:
          branch.branch_color || defaultColors[index % defaultColors.length],
        type: "line" as const,
      }));

      const totalRecords = leaveData.length;

      return {
        success: true,
        chartType: "line",
        title: "Leave Comparison by Branch (12-Month Timeline)",
        data: {
          data: chartData,
          xAxisKey: "name",
          yAxisKey: "value",
          series,
        } as RechartsLineBarData,
        metadata: {
          totalRecords,
          dateRange: `${monthLabels[0]} - ${monthLabels[monthLabels.length - 1]}`,
          timelinePeriod: "12 months",
          branchCount: branches.length,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getLeaveComparisonByBranchData:", error);
      return {
        success: false,
        chartType: "line",
        title: "Leave Comparison by Branch (12-Month Timeline)",
        data: {
          data: [],
          xAxisKey: "name",
          yAxisKey: "value",
          series: [],
        } as RechartsLineBarData,
        metadata: {
          totalRecords: 0,
          timelinePeriod: "12 months",
          branchCount: 0,
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

export interface DashboardWidget {
  id?: number;
  title: string;
  type: string;
  data: any;
  category: "count" | "chart";
  chartType?: "line" | "bar" | "pie" | "gauge";
  order?: number;
}

/**
 * Chart configuration mapping system
 * This eliminates hardcoded array indices and makes the system more maintainable
 */
const CHART_CONFIGURATIONS: Record<ChartType, ChartConfig> = {
  [ChartType.ONBOARDING_PIE]: {
    type: ChartType.ONBOARDING_PIE,
    widgetType: "pie_chart",
    title: "Onboarding Pipeline Distribution",
    chartType: "pie",
    category: "chart",
    description:
      "Distribution of onboarding status: Completed and Verified users across branches",
    hasFilters: true,
    dataFetcher: getOnboardingPipelinePieData,
  },
  [ChartType.ONBOARDING_BAR]: {
    type: ChartType.ONBOARDING_BAR,
    widgetType: "bar_chart",
    title: "Onboarding Pipeline Status by Branch",
    chartType: "bar",
    category: "chart",
    description:
      "Compare onboarding status across branches with 2 series: Completed and Verified users",
    hasFilters: true,
    dataFetcher: getOnboardingPipelineBarData,
  },

  [ChartType.CONTRACT_GAUGE]: {
    type: ChartType.CONTRACT_GAUGE,
    widgetType: "gauge_chart",
    title: "Contract Health Metrics",
    chartType: "gauge",
    category: "chart",
    description:
      "Contract health gauge showing percentage of healthy vs problematic contracts",
    hasFilters: true,
    dataFetcher: getUserContractGaugeData,
  },
  [ChartType.LEAVE_COMPARISON]: {
    type: ChartType.LEAVE_COMPARISON,
    widgetType: "line_chart",
    title: "Leave Comparison by Branch (12-Month Timeline)",
    chartType: "line",
    category: "chart",
    description: "Monthly leave data comparison across branches",
    hasFilters: true,
    dataFetcher: getLeaveComparisonByBranchData,
  },
};

/**
 * Get chart configurations based on filters
 */
const getFilteredChartConfigs = (filters?: FilterOptions): ChartConfig[] => {
  let configs = Object.values(CHART_CONFIGURATIONS);

  // Filter by chart type if specified
  if (filters?.chartType) {
    configs = configs.filter(
      (config) => config.chartType === filters.chartType
    );
  }

  return configs;
};

/**
 * Create chart widget from configuration and data
 */
const createChartWidget = (
  config: ChartConfig,
  chartData: CommonChartResponse,
  index: number
): DashboardWidget => {
  // Generate dynamic ID based on chart type and index
  const baseId = 5; // Start chart IDs after the 4 count widgets
  const widgetId = baseId + index;

  return {
    id: widgetId,
    title: chartData.title || config.title,
    type: config.widgetType,
    data: {
      // Use the data from chartData.data directly to avoid duplication
      ...chartData.data,
      ...(config.hasFilters && { filters: { branchFilter: true } }),
      description: config.description,
    },
    category: config.category,
    chartType: config.chartType,
    order: widgetId, // Add the missing order property
  };
};

/**
 * Interface for User Statistics
 */
export interface UserStatistics {
  totalUsers: number;
  pendingUsers: number;
  activeUsers: number;
  ongoingUsers: number;
  completedUsers: number;
  verifiedUsers: number;
  deletedUsers: number;
}

/**
 * Interface for Employee Leave Statistics
 */
export interface EmployeeLeaveStatistics {
  employeesOnLeaveToday: number;
  employeesWithRequestedLeave: number;
}

/**
 * Interface for Change Request Statistics
 */
export interface ChangeRequestStatistics {
  totalChangeRequests: number;
  pendingChangeRequests: number;
  approvedChangeRequests: number;
  rejectedChangeRequests: number;
}

/**
 * Interface for Resignation Statistics
 */
export interface ResignationStatistics {
  totalResignations: number;
  pendingResignations: number;
  approvedResignations: number;
  processedResignations: number;
}

//  Interface for active branch & department statistics
export interface BranchDepartmentStatistics {
  activeBranches: number;
  activeDepartments: number;
}

/**
 * Interface for Rota Statistics
 */
export interface RotaStatistics {
  shiftSwapCount: number;
  dropShiftCount: number;
  totalShiftCount: number;
}

/**
 * Interface for Department Statistics (Simple number cards)
 */
export interface DepartmentStatistics {
  totalDepartmentCount: number;
  totalContractAssigned: number;
  totalContractUnassigned: number;
}

/**
 * Interface for Branch Statistics (Simple number cards)
 */
export interface BranchStatistics {
  totalBranchCount: number;
  totalDocumentAssigned: number;
  totalDocumentUnassigned: number;
}

/**
 * Interface for Role Statistics (Simple number cards)
 */
export interface RoleStatistics {
  totalRoleCount: number;
  activeRoleCount: number;
  inactiveRoleCount: number;
}

/**
 * Interface for Leave Type & Policy Statistics (Simple number cards)
 */
export interface LeaveTypePolicyStatistics {
  totalLeaveTypes: number;
  totalLeavePolicies: number;
  activeLeaveTypes: number;
  activeLeavePolicies: number;
}

/**
 * Interface for Document Statistics (Simple number cards)
 */
export interface DocumentStatistics {
  totalDocuments: number;
  totalDocumentCategories: number;
  activeDocumentCategories: number;
}

/**
 * Interface for Recipe Statistics (Simple number cards)
 */
export interface RecipeStatistics {
  totalRecipes: number;
  publicRecipes: number;
  privateRecipes: number;
}

/**
 * Interface for Holiday Statistics (Simple number cards)
 */
export interface HolidayStatistics {
  totalHolidays: number;
  totalHolidayTypes: number;
  activeHolidayTypes: number;
}

/**
 * Interface for Storage Statistics (Simple number cards)
 */
export interface StorageStatistics {
  totalStorageUsed: number;
  totalFiles: number;
  storageUnit: string;
  storagePercentage: number;
}

/**
 * Interface for Complete Dashboard Statistics
 */
export interface DashboardStatistics {
  userStats: UserStatistics;
  leaveStats: EmployeeLeaveStatistics;
  changeRequestStats: ChangeRequestStatistics;
  resignationStats: ResignationStatistics;
  rotaStats: RotaStatistics;
  branchDeptStats: BranchDepartmentStatistics;
  departmentStats: DepartmentStatistics;
  branchStats: BranchStatistics;
  roleStats: RoleStatistics;
  leaveTypePolicyStats: LeaveTypePolicyStatistics;
  documentStats: DocumentStatistics;
  recipeStats: RecipeStatistics;
  holidayStats: HolidayStatistics;
  storageStats: StorageStatistics;
}

/**
 * Get user-specific dashboard widgets
 * Returns widgets relevant to regular users with comprehensive statistics
 */
export const getUserDashboardWidgets = async (
  organizationId: string,
  userId: number,
  filters?: FilterOptions
): Promise<DashboardWidget[]> => {
  // Validate input parameters
  validateParameters(organizationId, userId);

  try {
    // Static count widgets (no database calls needed)
    const countWidgets: DashboardWidget[] = [
      {
        id: 1,
        title: "My Tasks",
        type: "task_summary",
        data: {
          pending: 5,
          completed: 12,
          overdue: 2,
        },
        order: 1,
        category: "count",
      },
      {
        id: 2,
        title: "My Leave Balance",
        type: "leave_balance",
        data: {
          available: 15,
          used: 10,
          pending: 2,
        },
        order: 2,
        category: "count",
      },
      {
        id: 3,
        title: "User Activity",
        type: "user_activity",
        data: await getUserActivityStatistics(
          organizationId,
          filters?.activity_action
            ? { activity_action: filters.activity_action }
            : undefined
        ),
        order: 3,
        category: "count",
      },
      {
        id: 4,
        title: "Notification Center",
        type: "notification_center",
        data: await getNotificationCenterStatistics(
          organizationId,
          filters?.notification_status
            ? { notification_status: filters.notification_status }
            : undefined
        ),
        order: 4,
        category: "count",
      },
      {
        id: 5,
        title: "My Performance",
        type: "performance_chart",
        data: {
          currentMonth: 85,
          lastMonth: 78,
          trend: "up",
        },
        order: 5,
        category: "count",
      },
    ];

    // Get filtered chart configurations based on user filters
    const chartConfigs = getFilteredChartConfigs(filters);

    // Create data fetching promises dynamically based on configurations
    const chartDataPromises = chartConfigs.map((config) => ({
      type: config.type,
      promise: config.dataFetcher(organizationId, filters),
    }));

    // Use Promise.allSettled to handle partial failures gracefully
    const chartResults = await Promise.allSettled(
      chartDataPromises.map((item) => item.promise)
    );

    // Create chart widgets dynamically using the mapping system
    const chartWidgets: DashboardWidget[] = [];

    chartResults.forEach((result, index) => {
      const config = chartConfigs[index];
      const chartType = chartDataPromises[index].type;

      if (result.status === "fulfilled" && result.value.success) {
        // Create widget using the configuration and data
        const widget = createChartWidget(config, result.value, index);
        chartWidgets.push(widget);
      } else {
        // Log failed chart data fetches for monitoring
        const errorMessage =
          result.status === "rejected"
            ? result.reason
            : "Chart data fetch returned unsuccessful result";
        console.error(
          `Chart data fetch failed for ${chartType}:`,
          errorMessage
        );
      }
    });

    // Get comprehensive statistics for admin users
    let statisticsData = null;
    try {
      statisticsData = await getComprehensiveStatistics(organizationId);
    } catch (error) {
      console.error(
        "Failed to fetch statistics data, continuing with basic widgets:",
        error
      );
    }

    // Add user statistics widget if statistics data is available
    if (statisticsData) {
      countWidgets.push({
        id: 5,
        title: "User Statistics",
        type: "user_statistics",
        data: statisticsData.userStats,
        order: 5,
        category: "count",
      });

      // Add rota statistics widget
      countWidgets.push({
        id: 6,
        title: "Rota Statistics",
        type: "rota_statistics",
        data: statisticsData.rotaStats,
        order: 6,
        category: "count",
      });
      // Add branch & department statistics widget
      countWidgets.push({
        id: 7,
        title: "Branch & Department Statistics",
        type: "branch_department_statistics",
        data: statisticsData.branchDeptStats,
        order: 7,
        category: "count",
      });

      // Add department statistics widget (Simple number cards)
      countWidgets.push({
        id: 8,
        title: "Departments",
        type: "department_statistics",
        data: statisticsData.departmentStats,
        order: 8,
        category: "count",
      });

      // Add branch statistics widget (Simple number cards)
      countWidgets.push({
        id: 9,
        title: "Branches",
        type: "branch_statistics",
        data: statisticsData.branchStats,
        order: 9,
        category: "count",
      });

      // Add role statistics widget (Simple number cards)
      countWidgets.push({
        id: 10,
        title: "Roles",
        type: "role_statistics",
        data: statisticsData.roleStats,
        order: 10,
        category: "count",
      });

      // Add storage statistics widget (Simple number cards)
      countWidgets.push({
        id: 11,
        title: "Storage Usage",
        type: "storage_statistics",
        data: statisticsData.storageStats,
        order: 11,
        category: "count",
      });

      // Add leave type & policy statistics widget (Simple number cards)
      countWidgets.push({
        id: 12,
        title: "Leave Types & Policies",
        type: "leave_type_policy_statistics",
        data: statisticsData.leaveTypePolicyStats,
        order: 12,
        category: "count",
      });

      // Add document statistics widget (Simple number cards)
      countWidgets.push({
        id: 13,
        title: "Documents",
        type: "document_statistics",
        data: statisticsData.documentStats,
        order: 13,
        category: "count",
      });

      // Add recipe statistics widget (Simple number cards)
      countWidgets.push({
        id: 14,
        title: "Recipes",
        type: "recipe_statistics",
        data: statisticsData.recipeStats,
        order: 14,
        category: "count",
      });

      // Add holiday statistics widget (Simple number cards)
      countWidgets.push({
        id: 15,
        title: "Holidays",
        type: "holiday_statistics",
        data: statisticsData.holidayStats,
        order: 15,
        category: "count",
      });
    }

    // Combine widgets based on filter requirements
    let allWidgets: DashboardWidget[] = [];

    if (filters?.widgetType === "charts") {
      allWidgets = chartWidgets;
    } else if (filters?.widgetType === "counts") {
      allWidgets = countWidgets;
    } else {
      // Default: return both count and chart widgets
      allWidgets = [...countWidgets, ...chartWidgets];
    }

    // Apply chart type filtering if specified
    if (filters?.widgetType === "charts" && filters?.chartType) {
      allWidgets = allWidgets.filter(
        (widget) => widget.chartType === filters.chartType
      );
    }

    return allWidgets;
  } catch (error) {
    console.error("Error in getUserDashboardWidgets:", error);
    // Return at least the count widgets on error to provide partial functionality
    if (filters?.widgetType !== "charts") {
      return [
        {
          id: 1,
          title: "My Tasks",
          type: "task_summary",
          data: { pending: 0, completed: 0, overdue: 0 },
          category: "count",
          order: 1,
        },
      ];
    }
    throw error;
  }
};

/**
 * Get admin-specific dashboard widgets
 * Returns widgets relevant to administrators
 */
export const getDsrDashboardWidgets = async (
  organizationId: string,
  userId: number,
  filters?: any
): Promise<DashboardWidget[]> => {
  // Suppress unused parameter warnings - these parameters are kept for API consistency
  void userId;
  void filters;
  try {
    const widgets: DashboardWidget[] = [];

    // Get comprehensive statistics for admin users
    let statisticsData = null;
    try {
      statisticsData = await getComprehensiveStatistics(organizationId);
    } catch (error) {
      console.warn(
        "Failed to fetch statistics data, continuing with basic widgets:",
        error
      );
    }

    // DSR-specific widgets
    widgets.push({
      id: 1,
      title: "DSR Summary",
      type: "dsr_stats",
      data: {
        totalDSRs: 150,
        pendingDSRs: 25,
        approvedDSRs: 120,
        rejectedDSRs: 5,
      },
      order: 1,
      category: "count",
    });

    widgets.push({
      id: 2,
      title: "Recent DSR Activities",
      type: "dsr_activities",
      data: {
        recentSubmissions: 12,
        pendingApprovals: 8,
        todaysSubmissions: 5,
      },
      order: 2,
      category: "count",
    });

    widgets.push({
      id: 3,
      title: "DSR Performance",
      type: "dsr_performance",
      data: {
        onTimeSubmissions: 85,
        lateSubmissions: 10,
        averageRating: 4.2,
      },
      order: 3,
      category: "count",
    });

    widgets.push({
      id: 4,
      title: "Team DSR Status",
      type: "team_dsr_status",
      data: {
        totalTeamMembers: 15,
        submittedToday: 12,
        pendingToday: 3,
      },
      order: 4,
      category: "count",
    });

    // Add employee leave statistics widget for admin dashboard
    if (statisticsData) {
      widgets.push({
        id: 5,
        title: "Employee Leave Status",
        type: "employee_leave_statistics",
        data: statisticsData.leaveStats, // Direct object assignment
        order: 5,
        category: "count",
      });
    }

    return widgets;
  } catch (error) {
    console.error("Error in getDsrDashboardWidgets:", error);
    throw error;
  }
};

/**
 * Get setup-specific dashboard widgets
 * Returns widgets relevant to system setup
 */
export const getSetupDashboardWidgets = async (
  organizationId: string,
  userId: number,
  filters?: any
): Promise<DashboardWidget[]> => {
  // Suppress unused parameter warnings - these parameters are kept for API consistency
  void userId;
  void filters;
  try {
    const widgets: DashboardWidget[] = [];

    // Get comprehensive statistics for admin users
    let statisticsData = null;
    try {
      statisticsData = await getComprehensiveStatistics(organizationId);
    } catch (error) {
      console.warn(
        "Failed to fetch statistics data, continuing with basic widgets:",
        error
      );
    }

    // Setup-specific widgets
    widgets.push({
      id: 1,
      title: "System Configuration",
      type: "system_config",
      data: {
        completedSteps: 8,
        totalSteps: 12,
        progressPercentage: 67,
      },
      order: 1,
      category: "count",
    });

    widgets.push({
      id: 2,
      title: "User Setup",
      type: "user_setup",
      data: {
        totalUsers: 25,
        activeUsers: 20,
        pendingInvitations: 5,
      },
      order: 2,
      category: "count",
    });

    widgets.push({
      id: 3,
      title: "Module Configuration",
      type: "module_config",
      data: {
        enabledModules: 8,
        totalModules: 12,
        pendingConfiguration: 4,
      },
      order: 3,
      category: "count",
    });

    widgets.push({
      id: 4,
      title: "Integration Status",
      type: "integration_status",
      data: {
        connectedServices: 3,
        totalServices: 6,
        failedConnections: 1,
      },
      order: 4,
      category: "count",
    });

    // Add change request and resignation statistics for setup dashboard
    if (statisticsData) {
      widgets.push({
        id: 5,
        title: "Change Requests",
        type: "change_request_statistics",
        data: statisticsData.changeRequestStats,
        order: 5,
        category: "count",
      });

      widgets.push({
        id: 6,
        title: "Resignations",
        type: "resignation_statistics",
        data: statisticsData.resignationStats,
        order: 6,
        category: "count",
      });
    }

    return widgets;
  } catch (error) {
    console.error("Error in getSetupDashboardWidgets:", error);
    throw error;
  }
};

/**
 * Get department statistics (Active departments + Contract counts)
 */
const getDepartmentStatistics = async (
  organizationId: string
): Promise<DepartmentStatistics> => {
  try {
    // Get total department count
    const totalDepartmentCount = await Department.count({
      where: {
        organization_id: organizationId,
        department_status: "active",
      },
    });

    // Get total users in organization
    const totalUsers = await User.count({
      where: {
        organization_id: organizationId,
      },
    });

    // Get total contract assigned count
    const totalContractAssigned = await UserEmploymentContract.count({
      include: [
        {
          model: User,
          as: "user_employment_contract",
          where: {
            organization_id: organizationId,
          },
          attributes: [],
          required: true,
        },
      ],
    });

    // Calculate unassigned contracts (users without contracts)
    const totalContractUnassigned = totalUsers - totalContractAssigned;

    return {
      totalDepartmentCount,
      totalContractAssigned,
      totalContractUnassigned,
    };
  } catch (error: any) {
    console.error("Error fetching department statistics:", error);
    console.error(
      "Department statistics error details:",
      error?.message || error
    );
    return {
      totalDepartmentCount: 0,
      totalContractAssigned: 0,
      totalContractUnassigned: 0,
    };
  }
};

/**
 * Get branch statistics (Active branches + Document counts)
 */
const getBranchStatistics = async (
  organizationId: string
): Promise<BranchStatistics> => {
  try {
    // Get total branch count
    const totalBranchCount = await Branch.count({
      where: {
        organization_id: organizationId,
        branch_status: "active",
      },
    });

    // Get total users in organization
    const totalUsers = await User.count({
      where: {
        organization_id: organizationId,
      },
    });

    // Get total document assigned count using branch assignments
    const documentAssignedResult = await sequelize.query(
      `SELECT COUNT(*) as count
       FROM nv_document_category_branch dcb
       INNER JOIN nv_document_category dc ON dcb.category_id = dc.id
       WHERE dc.organization_id = :organizationId
       AND dcb.document_category_branch_status = 'active'`,
      {
        replacements: { organizationId },
        type: QueryTypes.SELECT,
      }
    );
    const totalDocumentAssigned = (documentAssignedResult[0] as any).count;

    // Calculate unassigned documents based on branch assignments
    // Get total users in branches that have document assignments
    const usersInAssignedBranchesResult = await sequelize.query(
      `SELECT COUNT(DISTINCT u.id) as count
       FROM nv_users u
       INNER JOIN nv_document_category_branch dcb ON u.branch_id = dcb.branch_id
       INNER JOIN nv_document_category dc ON dcb.category_id = dc.id
       WHERE dc.organization_id = :organizationId
       AND dcb.document_category_branch_status = 'active'
       AND u.organization_id = :organizationId`,
      {
        replacements: { organizationId },
        type: QueryTypes.SELECT,
      }
    );
    const usersInAssignedBranches = (usersInAssignedBranchesResult[0] as any)
      .count;

    // Calculate unassigned as users who could have assignments but don't
    const totalDocumentUnassigned = Math.max(
      0,
      totalUsers - usersInAssignedBranches
    );

    return {
      totalBranchCount,
      totalDocumentAssigned,
      totalDocumentUnassigned,
    };
  } catch (error: any) {
    console.error("Error fetching branch statistics:", error);
    console.error("Branch statistics error details:", error?.message || error);
    return {
      totalBranchCount: 0,
      totalDocumentAssigned: 0,
      totalDocumentUnassigned: 0,
    };
  }
};

/**
 * Get comprehensive statistics data for dashboard widgets
 * This function aggregates all statistics in one place following the existing pattern
 */
const getComprehensiveStatistics = async (organizationId: string) => {
  try {
    // Execute all statistics queries in parallel for better performance
    const [
      userStats,
      leaveStats,
      changeRequestStats,
      resignationStats,
      rotaStats,
      branchDeptStats,
      departmentStats,
      branchStats,
      roleStats,
      leaveTypePolicyStats,
      documentStats,
      recipeStats,
      holidayStats,
      storageStats,
    ] = await Promise.all([
      getUserStatistics(organizationId),
      getEmployeeLeaveStatistics(organizationId),
      getChangeRequestStatistics(organizationId),
      getResignationStatistics(organizationId),
      getRotaStatistics(organizationId),
      getBranchDepartmentStatistics(organizationId),
      getDepartmentStatistics(organizationId),
      getBranchStatistics(organizationId),
      getRoleStatistics(organizationId),
      getLeaveTypePolicyStatistics(organizationId),
      getDocumentStatistics(organizationId),
      getRecipeStatistics(organizationId),
      getHolidayStatistics(organizationId),
      getStorageStatistics(organizationId),
    ]);

    return {
      userStats,
      leaveStats,
      changeRequestStats,
      resignationStats,
      rotaStats,
      branchDeptStats,
      departmentStats,
      branchStats,
      roleStats,
      leaveTypePolicyStats,
      documentStats,
      recipeStats,
      holidayStats,
      storageStats,
    };
  } catch (error) {
    console.error("Error in getComprehensiveStatistics:", error);
    throw error;
  }
};

/**
 * Get user statistics for the organization
 */
const getUserStatistics = async (
  organizationId: string
): Promise<UserStatistics> => {
  try {
    // fetch user statistics
    const userStatsQuery = await User.findOne({
      attributes: [
        [sequelize.fn("COUNT", sequelize.col("*")), "total_users"],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.PENDING}' THEN 1 ELSE 0 END`
            )
          ),
          "total_pending",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.ACTIVE}' THEN 1 ELSE 0 END`
            )
          ),
          "total_active",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.ONGOING}' THEN 1 ELSE 0 END`
            )
          ),
          "total_ongoing",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.COMPLETED}' THEN 1 ELSE 0 END`
            )
          ),
          "total_completed",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.VERIFIED}' THEN 1 ELSE 0 END`
            )
          ),
          "total_verified",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN user_status = '${user_status.DELETED}' THEN 1 ELSE 0 END`
            )
          ),
          "total_deleted",
        ],
      ],
      where: {
        organization_id: organizationId,
      },
      raw: true,
      subQuery: false,
    });

    return {
      totalUsers: parseInt((userStatsQuery as any)?.total_users as string) || 0,
      pendingUsers:
        parseInt((userStatsQuery as any)?.total_pending as string) || 0,
      activeUsers:
        parseInt((userStatsQuery as any)?.total_active as string) || 0,
      ongoingUsers:
        parseInt((userStatsQuery as any)?.total_ongoing as string) || 0,
      completedUsers:
        parseInt((userStatsQuery as any)?.total_completed as string) || 0,
      verifiedUsers:
        parseInt((userStatsQuery as any)?.total_verified as string) || 0,
      deletedUsers:
        parseInt((userStatsQuery as any)?.total_deleted as string) || 0,
    };
  } catch (error) {
    console.error("Error fetching user statistics:", error);
    // Return default values on error to prevent dashboard from breaking
    return {
      totalUsers: 0,
      pendingUsers: 0,
      activeUsers: 0,
      ongoingUsers: 0,
      completedUsers: 0,
      verifiedUsers: 0,
      deletedUsers: 0,
    };
  }
};

/**
 * Get employee leave statistics for the organization
 */
const getEmployeeLeaveStatistics = async (
  organizationId: string
): Promise<EmployeeLeaveStatistics> => {
  try {
    const today = moment().format("YYYY-MM-DD");

    // Count employees on leave today (approved leave requests that include today's date)
    const employeesOnLeaveToday = await UserRequest.count({
      include: [
        {
          model: User,
          as: "request_from_users", // Use the proper association alias
          where: {
            organization_id: organizationId,
          },
          attributes: [], // Don't select user attributes for better performance
        },
      ],
      where: {
        request_status: "approved",
        start_date: { [Op.lte]: today },
        end_date: { [Op.gte]: today },
      },
    });

    // Count employees with pending leave requests
    const employeesWithRequestedLeave = await UserRequest.count({
      include: [
        {
          model: User,
          as: "request_from_users", // Use the proper association alias
          where: {
            organization_id: organizationId,
          },
          attributes: [], // Don't select user attributes for better performance
        },
      ],
      where: {
        request_status: "pending",
      },
    });

    return {
      employeesOnLeaveToday,
      employeesWithRequestedLeave,
    };
  } catch (error) {
    console.error("Error fetching employee leave statistics:", error);
    // Return default values on error
    return {
      employeesOnLeaveToday: 0,
      employeesWithRequestedLeave: 0,
    };
  }
};

/**
 * Get change request statistics for the organization
 */
const getChangeRequestStatistics = async (
  organizationId: string
): Promise<ChangeRequestStatistics> => {
  try {
    const changeRequestStatsQuery = await ChangeRequest.findAll({
      attributes: [
        [sequelize.fn("COUNT", sequelize.col("*")), "totalChangeRequests"],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN change_request_status = 'pending' THEN 1 ELSE 0 END`
            )
          ),
          "pendingChangeRequests",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN change_request_status = 'approved' THEN 1 ELSE 0 END`
            )
          ),
          "approvedChangeRequests",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN change_request_status = 'rejected' THEN 1 ELSE 0 END`
            )
          ),
          "rejectedChangeRequests",
        ],
      ],
      include: [
        {
          model: User,
          as: "change_request_user", // Use the proper association alias
          where: { organization_id: organizationId },
          attributes: [], // Don't select user attributes to avoid GROUP BY issues
        },
      ],
      raw: true,
    });

    const stats = changeRequestStatsQuery[0] as any;

    return {
      totalChangeRequests: parseInt(stats?.totalChangeRequests) || 0,
      pendingChangeRequests: parseInt(stats?.pendingChangeRequests) || 0,
      approvedChangeRequests: parseInt(stats?.approvedChangeRequests) || 0,
      rejectedChangeRequests: parseInt(stats?.rejectedChangeRequests) || 0,
    };
  } catch (error) {
    console.error("Error fetching change request statistics:", error);
    // Return default values on error
    return {
      totalChangeRequests: 0,
      pendingChangeRequests: 0,
      approvedChangeRequests: 0,
      rejectedChangeRequests: 0,
    };
  }
};

/**
 * Get resignation statistics for the organization
 */
const getResignationStatistics = async (
  organizationId: string
): Promise<ResignationStatistics> => {
  try {
    // Use simpler approach without JOIN to avoid GROUP BY issues
    const resignationStatsQuery = await Resignation.findAll({
      attributes: [
        [sequelize.fn("COUNT", sequelize.col("*")), "totalResignations"],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN resignation_status = 'pending' THEN 1 ELSE 0 END`
            )
          ),
          "pendingResignations",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN resignation_status = 'accepted' THEN 1 ELSE 0 END`
            )
          ),
          "approvedResignations",
        ],
        [
          sequelize.fn(
            "SUM",
            sequelize.literal(
              `CASE WHEN resignation_status = 'accepted' OR resignation_status = 'in-discussion' THEN 1 ELSE 0 END`
            )
          ),
          "processedResignations",
        ],
      ],
      include: [
        {
          model: User,
          as: "resign_user",
          where: { organization_id: organizationId },
          attributes: [],
        },
      ],
      raw: true,
    });

    const stats = resignationStatsQuery[0] as any;

    return {
      totalResignations: parseInt(stats?.totalResignations) || 0,
      pendingResignations: parseInt(stats?.pendingResignations) || 0,
      approvedResignations: parseInt(stats?.approvedResignations) || 0,
      processedResignations: parseInt(stats?.processedResignations) || 0,
    };
  } catch (error) {
    console.error("Error fetching resignation statistics:", error);
    // Return default values on error
    return {
      totalResignations: 0,
      pendingResignations: 0,
      approvedResignations: 0,
      processedResignations: 0,
    };
  }
};

/**
 * Get rota (shift) statistics for the organization
 * - shiftSwapCount  : Shifts that are marked as swap (isSwap = true) and not deleted
 * - dropShiftCount  : Shifts that are marked as dropped (isDropped = true)
 * - totalShiftCount : All shifts that are not deleted
 */
const getRotaStatistics = async (
  organizationId: string
): Promise<RotaStatistics> => {
  try {
    // Shift swap count (isSwap = true and status != deleted)
    const [swapResult]: any = await sequelize.query(
      `SELECT COUNT(id) AS count FROM shifts WHERE isSwap = 1 AND status <> 'deleted' AND organization_id = :orgId`,
      {
        replacements: { orgId: organizationId },
        type: QueryTypes.SELECT,
      }
    );

    // Dropped shift count (isDropped = true and status != deleted)
    const [dropResult]: any = await sequelize.query(
      `SELECT COUNT(id) AS count FROM shifts WHERE isDropped = 1  AND organization_id = :orgId`,
      {
        replacements: { orgId: organizationId },
        type: QueryTypes.SELECT,
      }
    );

    // Total shifts (status != deleted)
    const [totalResult]: any = await sequelize.query(
      `SELECT COUNT(id) AS count FROM shifts WHERE status <> 'deleted' AND organization_id = :orgId`,
      {
        replacements: { orgId: organizationId },
        type: QueryTypes.SELECT,
      }
    );

    return {
      shiftSwapCount: parseInt(swapResult?.count as string) || 0,
      dropShiftCount: parseInt(dropResult?.count as string) || 0,
      totalShiftCount: parseInt(totalResult?.count as string) || 0,
    } as RotaStatistics;
  } catch (error) {
    console.error("Error fetching rota statistics:", error);
    // Return default values on error to prevent dashboard from breaking
    return {
      shiftSwapCount: 0,
      dropShiftCount: 0,
      totalShiftCount: 0,
    };
  }
};

/**
 * Get user activity statistics with filters
 */
const getUserActivityStatistics = async (
  organizationId: string,
  filters?: { activity_action?: string }
): Promise<any> => {
  try {
    const baseQuery = `
      SELECT
        activity_action,
        COUNT(*) as count
      FROM nv_activities a
      INNER JOIN nv_users u ON a.created_by = u.id
      WHERE u.organization_id = :organizationId
    `;

    let whereClause = "";
    const replacements: any = { organizationId };

    // Add filter for specific activity action
    if (filters?.activity_action) {
      whereClause += " AND a.activity_action = :activity_action";
      replacements.activity_action = filters.activity_action;
    }

    const finalQuery = baseQuery + whereClause + " GROUP BY activity_action";

    const results = await sequelize.query(finalQuery, {
      replacements,
      type: QueryTypes.SELECT,
    });

    // Transform results into the expected format
    const stats = {
      totalCreated: 0,
      totalUpdated: 0,
      totalLogin: 0,
      totalLogout: 0,
    };

    (results as any[]).forEach((row: any) => {
      switch (row.activity_action) {
        case "created":
          stats.totalCreated = parseInt(row.count);
          break;
        case "updated":
          stats.totalUpdated = parseInt(row.count);
          break;
        case "login":
          stats.totalLogin = parseInt(row.count);
          break;
        case "logout":
          stats.totalLogout = parseInt(row.count);
          break;
      }
    });

    return stats;
  } catch (error: any) {
    console.error("Error fetching user activity statistics:", error);
    return {
      totalCreated: 0,
      totalUpdated: 0,
      totalLogin: 0,
      totalLogout: 0,
    };
  }
};

/**
 * Get notification center statistics with filters
 */
const getNotificationCenterStatistics = async (
  organizationId: string,
  filters?: { notification_status?: string }
): Promise<any> => {
  try {
    const baseQuery = `
      SELECT
        notification_status,
        COUNT(*) as count
      FROM notifications n
      INNER JOIN nv_users u ON n.to_user_id = u.id
      WHERE u.organization_id = :organizationId
        AND n.notification_status != 'deleted'
    `;

    let whereClause = "";
    const replacements: any = { organizationId };

    // Add filter for specific notification status
    if (filters?.notification_status) {
      whereClause += " AND n.notification_status = :notification_status";
      replacements.notification_status = filters.notification_status;
    }

    const finalQuery =
      baseQuery + whereClause + " GROUP BY notification_status";

    const results = await sequelize.query(finalQuery, {
      replacements,
      type: QueryTypes.SELECT,
    });

    // Transform results into the expected format
    const stats = {
      totalRead: 0,
      totalPending: 0,
    };

    (results as any[]).forEach((row: any) => {
      switch (row.notification_status) {
        case "read":
          stats.totalRead = parseInt(row.count);
          break;
        case "sent":
        case "pending":
          stats.totalPending = parseInt(row.count);
          break;
      }
    });

    return stats;
  } catch (error: any) {
    console.error("Error fetching notification center statistics:", error);
    return {
      totalRead: 0,
      totalPending: 0,
    };
  }
};

/**
 * Get active branch and department counts for the organization
 */
const getBranchDepartmentStatistics = async (
  organizationId: string
): Promise<BranchDepartmentStatistics> => {
  try {
    const [activeBranches, activeDepartments] = await Promise.all([
      Branch.count({
        where: { organization_id: organizationId, branch_status: "active" },
      }),
      Department.count({
        where: { organization_id: organizationId, department_status: "active" },
      }),
    ]);

    return {
      activeBranches,
      activeDepartments,
    } as BranchDepartmentStatistics;
  } catch (error) {
    console.error("Error fetching branch/department statistics:", error);
    return { activeBranches: 0, activeDepartments: 0 };
  }
};

/**
 * Get role statistics for the organization
 */
const getRoleStatistics = async (
  organizationId: string
): Promise<RoleStatistics> => {
  try {
    // Count roles from both old and new role systems
    const [oldRoles, newRoles] = await Promise.all([
      Role.count({
        where: { role_status: "active" },
      }),
      MORole.count({
        where: {
          organization_id: organizationId,
          role_status: "active",
        },
      }),
    ]);

    const [oldInactiveRoles, newInactiveRoles] = await Promise.all([
      Role.count({
        where: { role_status: "inactive" },
      }),
      MORole.count({
        where: {
          organization_id: organizationId,
          role_status: "inactive",
        },
      }),
    ]);

    const activeRoleCount = oldRoles + newRoles;
    const inactiveRoleCount = oldInactiveRoles + newInactiveRoles;
    const totalRoleCount = activeRoleCount + inactiveRoleCount;

    return {
      totalRoleCount,
      activeRoleCount,
      inactiveRoleCount,
    };
  } catch (error) {
    console.error("Error fetching role statistics:", error);
    return {
      totalRoleCount: 0,
      activeRoleCount: 0,
      inactiveRoleCount: 0,
    };
  }
};

/**
 * Get leave type and policy statistics for the organization
 */
const getLeaveTypePolicyStatistics = async (
  organizationId: string
): Promise<LeaveTypePolicyStatistics> => {
  try {
    const [
      totalLeaveTypes,
      activeLeaveTypes,
      totalLeavePolicies,
      activeLeavePolicies,
    ] = await Promise.all([
      LeaveTypeModel.count({
        where: { organization_id: organizationId },
      }),
      LeaveTypeModel.count({
        where: {
          organization_id: organizationId,
          status: "active",
        },
      }),
      LeavePolicyModel.count(),
      LeavePolicyModel.count({
        where: { status: "active" },
      }),
    ]);

    return {
      totalLeaveTypes,
      totalLeavePolicies,
      activeLeaveTypes,
      activeLeavePolicies,
    };
  } catch (error) {
    console.error("Error fetching leave type/policy statistics:", error);
    return {
      totalLeaveTypes: 0,
      totalLeavePolicies: 0,
      activeLeaveTypes: 0,
      activeLeavePolicies: 0,
    };
  }
};

/**
 * Get document statistics for the organization
 */
const getDocumentStatistics = async (
  organizationId: string
): Promise<DocumentStatistics> => {
  try {
    const [totalDocuments, totalDocumentCategories, activeDocumentCategories] =
      await Promise.all([
        Item.count({
          where: {
            item_organization_id: organizationId,
            item_status: item_status.ACTIVE,
          },
        }),
        DocumentCategory.count({
          where: { organization_id: organizationId },
        }),
        DocumentCategory.count({
          where: {
            organization_id: organizationId,
            category_status: "active",
          },
        }),
      ]);

    return {
      totalDocuments,
      totalDocumentCategories,
      activeDocumentCategories,
    };
  } catch (error) {
    console.error("Error fetching document statistics:", error);
    return {
      totalDocuments: 0,
      totalDocumentCategories: 0,
      activeDocumentCategories: 0,
    };
  }
};

/**
 * Get recipe statistics for the organization
 * Note: Since no Recipe model was found, returning placeholder values
 */
const getRecipeStatistics = async (
  organizationId: string
): Promise<RecipeStatistics> => {
  try {
    // TODO: Implement when Recipe model is available
    // For now, return placeholder values
    console.log(
      "Recipe statistics not implemented - no Recipe model found for organization:",
      organizationId
    );

    return {
      totalRecipes: 0,
      publicRecipes: 0,
      privateRecipes: 0,
    };
  } catch (error) {
    console.error("Error fetching recipe statistics:", error);
    return {
      totalRecipes: 0,
      publicRecipes: 0,
      privateRecipes: 0,
    };
  }
};

/**
 * Get holiday statistics for the organization
 */
const getHolidayStatistics = async (
  organizationId: string
): Promise<HolidayStatistics> => {
  try {
    const [totalHolidays, totalHolidayTypes, activeHolidayTypes] =
      await Promise.all([
        HolidayPolicy.count({
          where: { holiday_policy_status: "active" },
        }),
        HolidayType.count({
          where: { organization_id: organizationId },
        }),
        HolidayType.count({
          where: {
            organization_id: organizationId,
            holiday_type_status: "active",
          },
        }),
      ]);

    return {
      totalHolidays,
      totalHolidayTypes,
      activeHolidayTypes,
    };
  } catch (error) {
    console.error("Error fetching holiday statistics:", error);
    return {
      totalHolidays: 0,
      totalHolidayTypes: 0,
      activeHolidayTypes: 0,
    };
  }
};

/**
 * Get storage statistics for the organization
 */
const getStorageStatistics = async (
  organizationId: string
): Promise<StorageStatistics> => {
  try {
    const storageResult = await sequelize.query(
      `SELECT
        SUM(item_size) as total_bytes,
        COUNT(*) as total_files
      FROM nv_items
      WHERE item_organization_id = :organization_id
      AND item_status = :item_status`,
      {
        replacements: {
          organization_id: organizationId,
          item_status: item_status.ACTIVE,
        },
        type: QueryTypes.SELECT,
      }
    );

    const usage = storageResult[0] as any;
    const totalBytes = parseInt(usage?.total_bytes) || 0;
    const totalFiles = parseInt(usage?.total_files) || 0;

    // Convert bytes to appropriate unit
    let size = totalBytes;
    let unit = "B";

    if (size >= 1024 * 1024 * 1024) {
      size = size / (1024 * 1024 * 1024);
      unit = "GB";
    } else if (size >= 1024 * 1024) {
      size = size / (1024 * 1024);
      unit = "MB";
    } else if (size >= 1024) {
      size = size / 1024;
      unit = "KB";
    }

    // Calculate percentage (assuming some limit - this would need to be configured)
    const storagePercentage = 0; // TODO: Calculate based on organization's storage limit

    return {
      totalStorageUsed: parseFloat(size.toFixed(2)),
      totalFiles,
      storageUnit: unit,
      storagePercentage,
    };
  } catch (error) {
    console.error("Error fetching storage statistics:", error);
    return {
      totalStorageUsed: 0,
      totalFiles: 0,
      storageUnit: "B",
      storagePercentage: 0,
    };
  }
};

export default {
  getUserDashboardWidgets,
  getDsrDashboardWidgets,
  getSetupDashboardWidgets,
};
