import { Segments, Jo<PERSON>, celebrate } from "celebrate";

export default {
    createModule: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                module: Joi.string().required(),
                module_name: Joi.string().required(),
                type: Joi.string().valid("module", "widget").required()
            }),
        }),
    updateModule: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                module: Joi.string().allow(null, ""),
                module_name: Joi.string().allow(null, ""),
                type: Joi.string().valid("module", "widget").allow(null, "")
            }),
        }),
};
