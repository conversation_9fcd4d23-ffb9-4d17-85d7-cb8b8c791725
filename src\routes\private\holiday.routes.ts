import { Router } from "express";
import holidayController from "../../controller/holiday.controller";
import holidayTypeValidator from "../../validators/holidayType.validator";
import { multerS3, uploadMulter } from "../../helper/upload.service";

const uploads3 = multerS3(process.env.NODE_ENV! || "development", "others");

const router: Router = Router();

router.post(
  "/create-holiday-type",
  holidayTypeValidator.createHolidayType(),
  holidayController.createHolidayType,
);
router.put(
  "/update-holiday-type/:holiday_type_id",
  holidayTypeValidator.updateHolidayType(),
  holidayController.updateHolidayType,
);
router.get(
  "/get-holiday-type/:holiday_type_id",
  holidayController.getHolidayType,
);
router.get("/get-holiday-type-list", holidayController.getHolidayTypeList);
router.delete(
  "/delete-holiday-type/:holiday_type_id",
  holidayController.deleteHolidayType,
);
router.put(
  "/change-holiday-type-status",
  holidayTypeValidator.ChangeHolidayTypeStatus(),
  holidayController.inactiveHolidayType,
);

router.post(
  "/add-holiday-policy/:holiday_type_id",
  holidayTypeValidator.addHolidayPolicy(),
  holidayController.createHolidayPolicy,
);
router.put(
  "/update-holiday-policy/:holiday_policy_id",
  holidayTypeValidator.updateHolidayPolicy(),
  holidayController.updateHolidayPolicy,
);
router.get(
  "/get-holiday-policy/:holiday_policy_id",
  holidayController.getHolidayPolicy,
);
router.delete(
  "/delete-holiday-policy/:holiday_policy_id",
  holidayController.removeHolidayPolicy,
);
router.post(
  "/import-holiday-policy/:holiday_type_id",
  uploads3.upload("holiday_xlsx"),
  holidayController.importHolidayPolicy,
);
router.get("/get-user-holiday-policy", holidayController.getUserHolidayPolicy);

export default router;
