"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface moduleAttributes {
  id: number;
  module: string;
  module_name: string;
  organization_id: string;
  type: string;
  created_by: number;
  updated_by: number;
}

export enum module_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum module_type {
  MODULE = "module",
  WIDGET = "widget",
}

export class MOModule
  extends Model<moduleAttributes, never>
  implements moduleAttributes
{
  id!: number;
  module!: string;
  module_name!: string;
  organization_id!: string;
  type!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    return this;
  }
}

MOModule.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    module: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    module_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.ENUM,
      values: Object.values(module_type),
      allowNull: false,
      defaultValue: module_type.MODULE,
    },
    organization_id: {
      type: DataTypes.STRING,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "mo_modules",
    modelName: "MOModule",
    timestamps: true,
  }
);

MOModule.addHook("afterUpdate", async (module: any) => {
  await addActivity("MOModule", "updated", module);
});

MOModule.addHook("afterCreate", async (module: MOModule) => {
  await addActivity("MOModule", "created", module);
});


