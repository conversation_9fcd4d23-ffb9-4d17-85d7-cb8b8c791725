"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface moduleAttributes {
  id: number;
  module: string;
  module_name: string;
  organization_id: string;
  type: string;
  order: number;
  created_by: number;
  updated_by: number;
}

export enum module_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum module_type {
  MODULE = "module",
  WIDGET = "widget",
}

export class MOModule
  extends Model<moduleAttributes, never>
  implements moduleAttributes
{
  id!: number;
  module!: string;
  module_name!: string;
  organization_id!: string;
  type!: string;
  order!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    return this;
  }
}

MOModule.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    module: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    module_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.ENUM,
      values: Object.values(module_type),
      allowNull: false,
      defaultValue: module_type.MODULE,
    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0, // Default order will be set to module id in hooks
    },
    organization_id: {
      type: DataTypes.STRING,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "mo_modules",
    modelName: "MOModule",
    timestamps: true,
  }
);

// Set default order to module ID after creation
MOModule.addHook("afterCreate", async (module: MOModule) => {
  // Set order to module ID if not already set
  if (module.order === 0) {
    await module.update({ order: module.id });
  }
  await addActivity("MOModule", "created", module);
});

MOModule.addHook("afterUpdate", async (module: any) => {
  await addActivity("MOModule", "updated", module);
});


