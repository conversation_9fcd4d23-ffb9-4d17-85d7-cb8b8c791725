"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
// import { Role } from "./MORole";
import { MOModule } from "./MOModule";
import { addActivity } from "../helper/queue.service";

interface permissionAttributes {
  id: number;
  role_id: number;
  module_id: number;
  permission: number;
  additional_permissions: string;
  platform: number;
  partial: boolean;
  status: status;
  created_by: number;
  updated_by: number;
  organization_id: string;
}

export enum status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export class MOPermission
  extends Model<permissionAttributes, never>
  implements permissionAttributes
{
  id!: number;
  role_id!: number;
  module_id!: number;
  permission!: number;
  additional_permissions!: string;
  partial!: boolean;
  platform!: number;
  status!: status;
  created_by!: number;
  updated_by!: number;
  organization_id!: string;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    return this;
  }
}

MOPermission.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    module_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    permission: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    additional_permissions: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    partial: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    platform: {
      type: DataTypes.INTEGER,
    },
    status: {
      type: DataTypes.ENUM,
      values: Object.values(status),
      allowNull: false,
      defaultValue: status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
    organization_id: {
      type: DataTypes.STRING,
    },
  },
  {
    sequelize: sequelize,
    tableName: "mo_permissions",
    modelName: "MOPermission",
    timestamps: true,
  }
);


MOModule.hasMany(MOPermission, { foreignKey: "module_id" });
MOPermission.belongsTo(MOModule, { foreignKey: "module_id", as: "module" });

MOPermission.addHook("afterUpdate", async (permission: any) => {
  await addActivity("MOPermission", "updated", permission);
});

MOPermission.addHook("afterCreate", async (permission: MOPermission) => {
  await addActivity("MOPermission", "created", permission);
});

MOPermission.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

